[{"index": 1, "id": "1", "frontend_id": "1", "title_en": "Two Sum", "title_cn": "两数之和", "title_slug": "two-sum", "difficulty": "简单", "difficulty_en": "EASY", "category": "哈希", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Hash Table", "name_cn": "哈希表", "slug": "hash-table"}], "tags_cn": ["数组", "哈希表"], "tags_en": ["Array", "Hash Table"], "url": "https://leetcode.cn/problems/two-sum/"}, {"index": 2, "id": "49", "frontend_id": "49", "title_en": "Group Anagrams", "title_cn": "字母异位词分组", "title_slug": "group-anagrams", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "哈希", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Hash Table", "name_cn": "哈希表", "slug": "hash-table"}, {"name_en": "String", "name_cn": "字符串", "slug": "string"}, {"name_en": "Sorting", "name_cn": "排序", "slug": "sorting"}], "tags_cn": ["数组", "哈希表", "字符串", "排序"], "tags_en": ["Array", "Hash Table", "String", "Sorting"], "url": "https://leetcode.cn/problems/group-anagrams/"}, {"index": 3, "id": "128", "frontend_id": "128", "title_en": "Longest Consecutive Sequence", "title_cn": "最长连续序列", "title_slug": "longest-consecutive-sequence", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "哈希", "paid_only": false, "tags": [{"name_en": "Union Find", "name_cn": "并查集", "slug": "union-find"}, {"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Hash Table", "name_cn": "哈希表", "slug": "hash-table"}], "tags_cn": ["并查集", "数组", "哈希表"], "tags_en": ["Union Find", "Array", "Hash Table"], "url": "https://leetcode.cn/problems/longest-consecutive-sequence/"}, {"index": 4, "id": "283", "frontend_id": "283", "title_en": "Move Zeroes", "title_cn": "移动零", "title_slug": "move-zeroes", "difficulty": "简单", "difficulty_en": "EASY", "category": "双指针", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Two Pointers", "name_cn": "双指针", "slug": "two-pointers"}], "tags_cn": ["数组", "双指针"], "tags_en": ["Array", "Two Pointers"], "url": "https://leetcode.cn/problems/move-zeroes/"}, {"index": 5, "id": "11", "frontend_id": "11", "title_en": "Container With Most Water", "title_cn": "盛最多水的容器", "title_slug": "container-with-most-water", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "双指针", "paid_only": false, "tags": [{"name_en": "Greedy", "name_cn": "贪心", "slug": "greedy"}, {"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Two Pointers", "name_cn": "双指针", "slug": "two-pointers"}], "tags_cn": ["贪心", "数组", "双指针"], "tags_en": ["Greedy", "Array", "Two Pointers"], "url": "https://leetcode.cn/problems/container-with-most-water/"}, {"index": 6, "id": "15", "frontend_id": "15", "title_en": "3Sum", "title_cn": "三数之和", "title_slug": "3sum", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "双指针", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Two Pointers", "name_cn": "双指针", "slug": "two-pointers"}, {"name_en": "Sorting", "name_cn": "排序", "slug": "sorting"}], "tags_cn": ["数组", "双指针", "排序"], "tags_en": ["Array", "Two Pointers", "Sorting"], "url": "https://leetcode.cn/problems/3sum/"}, {"index": 7, "id": "42", "frontend_id": "42", "title_en": "Trapping Rain Water", "title_cn": "接雨水", "title_slug": "trapping-rain-water", "difficulty": "困难", "difficulty_en": "HARD", "category": "双指针", "paid_only": false, "tags": [{"name_en": "<PERSON><PERSON>", "name_cn": "栈", "slug": "stack"}, {"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Two Pointers", "name_cn": "双指针", "slug": "two-pointers"}, {"name_en": "Dynamic Programming", "name_cn": "动态规划", "slug": "dynamic-programming"}, {"name_en": "Monotonic Stack", "name_cn": "单调栈", "slug": "monotonic-stack"}], "tags_cn": ["栈", "数组", "双指针", "动态规划", "单调栈"], "tags_en": ["<PERSON><PERSON>", "Array", "Two Pointers", "Dynamic Programming", "Monotonic Stack"], "url": "https://leetcode.cn/problems/trapping-rain-water/"}, {"index": 8, "id": "3", "frontend_id": "3", "title_en": "Longest Substring Without Repeating Characters", "title_cn": "无重复字符的最长子串", "title_slug": "longest-substring-without-repeating-characters", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "滑动窗口", "paid_only": false, "tags": [{"name_en": "Hash Table", "name_cn": "哈希表", "slug": "hash-table"}, {"name_en": "String", "name_cn": "字符串", "slug": "string"}, {"name_en": "Sliding Window", "name_cn": "滑动窗口", "slug": "sliding-window"}], "tags_cn": ["哈希表", "字符串", "滑动窗口"], "tags_en": ["Hash Table", "String", "Sliding Window"], "url": "https://leetcode.cn/problems/longest-substring-without-repeating-characters/"}, {"index": 9, "id": "438", "frontend_id": "438", "title_en": "Find All Anagrams in a String", "title_cn": "找到字符串中所有字母异位词", "title_slug": "find-all-anagrams-in-a-string", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "滑动窗口", "paid_only": false, "tags": [{"name_en": "Hash Table", "name_cn": "哈希表", "slug": "hash-table"}, {"name_en": "String", "name_cn": "字符串", "slug": "string"}, {"name_en": "Sliding Window", "name_cn": "滑动窗口", "slug": "sliding-window"}], "tags_cn": ["哈希表", "字符串", "滑动窗口"], "tags_en": ["Hash Table", "String", "Sliding Window"], "url": "https://leetcode.cn/problems/find-all-anagrams-in-a-string/"}, {"index": 10, "id": "560", "frontend_id": "560", "title_en": "Subarray Sum Equals K", "title_cn": "和为 K 的子数组", "title_slug": "subarray-sum-equals-k", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "子串", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Hash Table", "name_cn": "哈希表", "slug": "hash-table"}, {"name_en": "Prefix Sum", "name_cn": "前缀和", "slug": "prefix-sum"}], "tags_cn": ["数组", "哈希表", "前缀和"], "tags_en": ["Array", "Hash Table", "Prefix Sum"], "url": "https://leetcode.cn/problems/subarray-sum-equals-k/"}, {"index": 11, "id": "239", "frontend_id": "239", "title_en": "Sliding Window Maximum", "title_cn": "滑动窗口最大值", "title_slug": "sliding-window-maximum", "difficulty": "困难", "difficulty_en": "HARD", "category": "子串", "paid_only": false, "tags": [{"name_en": "Queue", "name_cn": "队列", "slug": "queue"}, {"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Sliding Window", "name_cn": "滑动窗口", "slug": "sliding-window"}, {"name_en": "Monotonic Queue", "name_cn": "单调队列", "slug": "monotonic-queue"}, {"name_en": "<PERSON><PERSON> (Priority Queue)", "name_cn": "堆（优先队列）", "slug": "heap-priority-queue"}], "tags_cn": ["队列", "数组", "滑动窗口", "单调队列", "堆（优先队列）"], "tags_en": ["Queue", "Array", "Sliding Window", "Monotonic Queue", "<PERSON><PERSON> (Priority Queue)"], "url": "https://leetcode.cn/problems/sliding-window-maximum/"}, {"index": 12, "id": "76", "frontend_id": "76", "title_en": "Minimum Window Substring", "title_cn": "最小覆盖子串", "title_slug": "minimum-window-substring", "difficulty": "困难", "difficulty_en": "HARD", "category": "子串", "paid_only": false, "tags": [{"name_en": "Hash Table", "name_cn": "哈希表", "slug": "hash-table"}, {"name_en": "String", "name_cn": "字符串", "slug": "string"}, {"name_en": "Sliding Window", "name_cn": "滑动窗口", "slug": "sliding-window"}], "tags_cn": ["哈希表", "字符串", "滑动窗口"], "tags_en": ["Hash Table", "String", "Sliding Window"], "url": "https://leetcode.cn/problems/minimum-window-substring/"}, {"index": 13, "id": "53", "frontend_id": "53", "title_en": "Maximum Subarray", "title_cn": "最大子数组和", "title_slug": "maximum-subarray", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "普通数组", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Divide and Conquer", "name_cn": "分治", "slug": "divide-and-conquer"}, {"name_en": "Dynamic Programming", "name_cn": "动态规划", "slug": "dynamic-programming"}], "tags_cn": ["数组", "分治", "动态规划"], "tags_en": ["Array", "Divide and Conquer", "Dynamic Programming"], "url": "https://leetcode.cn/problems/maximum-subarray/"}, {"index": 14, "id": "56", "frontend_id": "56", "title_en": "<PERSON><PERSON>", "title_cn": "合并区间", "title_slug": "merge-intervals", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "普通数组", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Sorting", "name_cn": "排序", "slug": "sorting"}], "tags_cn": ["数组", "排序"], "tags_en": ["Array", "Sorting"], "url": "https://leetcode.cn/problems/merge-intervals/"}, {"index": 15, "id": "189", "frontend_id": "189", "title_en": "Rotate Array", "title_cn": "轮转数组", "title_slug": "rotate-array", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "普通数组", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Math", "name_cn": "数学", "slug": "math"}, {"name_en": "Two Pointers", "name_cn": "双指针", "slug": "two-pointers"}], "tags_cn": ["数组", "数学", "双指针"], "tags_en": ["Array", "Math", "Two Pointers"], "url": "https://leetcode.cn/problems/rotate-array/"}, {"index": 16, "id": "238", "frontend_id": "238", "title_en": "Product of Array Except Self", "title_cn": "除自身以外数组的乘积", "title_slug": "product-of-array-except-self", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "普通数组", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Prefix Sum", "name_cn": "前缀和", "slug": "prefix-sum"}], "tags_cn": ["数组", "前缀和"], "tags_en": ["Array", "Prefix Sum"], "url": "https://leetcode.cn/problems/product-of-array-except-self/"}, {"index": 17, "id": "41", "frontend_id": "41", "title_en": "First Missing Positive", "title_cn": "缺失的第一个正数", "title_slug": "first-missing-positive", "difficulty": "困难", "difficulty_en": "HARD", "category": "普通数组", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Hash Table", "name_cn": "哈希表", "slug": "hash-table"}], "tags_cn": ["数组", "哈希表"], "tags_en": ["Array", "Hash Table"], "url": "https://leetcode.cn/problems/first-missing-positive/"}, {"index": 18, "id": "73", "frontend_id": "73", "title_en": "Set Matrix Zeroes", "title_cn": "矩阵置零", "title_slug": "set-matrix-zeroes", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "矩阵", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Hash Table", "name_cn": "哈希表", "slug": "hash-table"}, {"name_en": "Matrix", "name_cn": "矩阵", "slug": "matrix"}], "tags_cn": ["数组", "哈希表", "矩阵"], "tags_en": ["Array", "Hash Table", "Matrix"], "url": "https://leetcode.cn/problems/set-matrix-zeroes/"}, {"index": 19, "id": "54", "frontend_id": "54", "title_en": "Spiral Matrix", "title_cn": "螺旋矩阵", "title_slug": "spiral-matrix", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "矩阵", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Matrix", "name_cn": "矩阵", "slug": "matrix"}, {"name_en": "Simulation", "name_cn": "模拟", "slug": "simulation"}], "tags_cn": ["数组", "矩阵", "模拟"], "tags_en": ["Array", "Matrix", "Simulation"], "url": "https://leetcode.cn/problems/spiral-matrix/"}, {"index": 20, "id": "48", "frontend_id": "48", "title_en": "Rotate Image", "title_cn": "旋转图像", "title_slug": "rotate-image", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "矩阵", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Math", "name_cn": "数学", "slug": "math"}, {"name_en": "Matrix", "name_cn": "矩阵", "slug": "matrix"}], "tags_cn": ["数组", "数学", "矩阵"], "tags_en": ["Array", "Math", "Matrix"], "url": "https://leetcode.cn/problems/rotate-image/"}, {"index": 21, "id": "240", "frontend_id": "240", "title_en": "Search a 2D Matrix II", "title_cn": "搜索二维矩阵 II", "title_slug": "search-a-2d-matrix-ii", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "矩阵", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Binary Search", "name_cn": "二分查找", "slug": "binary-search"}, {"name_en": "Divide and Conquer", "name_cn": "分治", "slug": "divide-and-conquer"}, {"name_en": "Matrix", "name_cn": "矩阵", "slug": "matrix"}], "tags_cn": ["数组", "二分查找", "分治", "矩阵"], "tags_en": ["Array", "Binary Search", "Divide and Conquer", "Matrix"], "url": "https://leetcode.cn/problems/search-a-2d-matrix-ii/"}, {"index": 22, "id": "160", "frontend_id": "160", "title_en": "Intersection of Two Linked Lists", "title_cn": "相交链表", "title_slug": "intersection-of-two-linked-lists", "difficulty": "简单", "difficulty_en": "EASY", "category": "链表", "paid_only": false, "tags": [{"name_en": "Hash Table", "name_cn": "哈希表", "slug": "hash-table"}, {"name_en": "Linked List", "name_cn": "链表", "slug": "linked-list"}, {"name_en": "Two Pointers", "name_cn": "双指针", "slug": "two-pointers"}], "tags_cn": ["哈希表", "链表", "双指针"], "tags_en": ["Hash Table", "Linked List", "Two Pointers"], "url": "https://leetcode.cn/problems/intersection-of-two-linked-lists/"}, {"index": 23, "id": "206", "frontend_id": "206", "title_en": "Reverse Linked List", "title_cn": "反转链表", "title_slug": "reverse-linked-list", "difficulty": "简单", "difficulty_en": "EASY", "category": "链表", "paid_only": false, "tags": [{"name_en": "Recursion", "name_cn": "递归", "slug": "recursion"}, {"name_en": "Linked List", "name_cn": "链表", "slug": "linked-list"}], "tags_cn": ["递归", "链表"], "tags_en": ["Recursion", "Linked List"], "url": "https://leetcode.cn/problems/reverse-linked-list/"}, {"index": 24, "id": "234", "frontend_id": "234", "title_en": "Palindrome Linked List", "title_cn": "回文链表", "title_slug": "palindrome-linked-list", "difficulty": "简单", "difficulty_en": "EASY", "category": "链表", "paid_only": false, "tags": [{"name_en": "<PERSON><PERSON>", "name_cn": "栈", "slug": "stack"}, {"name_en": "Recursion", "name_cn": "递归", "slug": "recursion"}, {"name_en": "Linked List", "name_cn": "链表", "slug": "linked-list"}, {"name_en": "Two Pointers", "name_cn": "双指针", "slug": "two-pointers"}], "tags_cn": ["栈", "递归", "链表", "双指针"], "tags_en": ["<PERSON><PERSON>", "Recursion", "Linked List", "Two Pointers"], "url": "https://leetcode.cn/problems/palindrome-linked-list/"}, {"index": 25, "id": "141", "frontend_id": "141", "title_en": "Linked List Cycle", "title_cn": "环形链表", "title_slug": "linked-list-cycle", "difficulty": "简单", "difficulty_en": "EASY", "category": "链表", "paid_only": false, "tags": [{"name_en": "Hash Table", "name_cn": "哈希表", "slug": "hash-table"}, {"name_en": "Linked List", "name_cn": "链表", "slug": "linked-list"}, {"name_en": "Two Pointers", "name_cn": "双指针", "slug": "two-pointers"}], "tags_cn": ["哈希表", "链表", "双指针"], "tags_en": ["Hash Table", "Linked List", "Two Pointers"], "url": "https://leetcode.cn/problems/linked-list-cycle/"}, {"index": 26, "id": "142", "frontend_id": "142", "title_en": "Linked List Cycle II", "title_cn": "环形链表 II", "title_slug": "linked-list-cycle-ii", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "链表", "paid_only": false, "tags": [{"name_en": "Hash Table", "name_cn": "哈希表", "slug": "hash-table"}, {"name_en": "Linked List", "name_cn": "链表", "slug": "linked-list"}, {"name_en": "Two Pointers", "name_cn": "双指针", "slug": "two-pointers"}], "tags_cn": ["哈希表", "链表", "双指针"], "tags_en": ["Hash Table", "Linked List", "Two Pointers"], "url": "https://leetcode.cn/problems/linked-list-cycle-ii/"}, {"index": 27, "id": "21", "frontend_id": "21", "title_en": "Merge Two Sorted Lists", "title_cn": "合并两个有序链表", "title_slug": "merge-two-sorted-lists", "difficulty": "简单", "difficulty_en": "EASY", "category": "链表", "paid_only": false, "tags": [{"name_en": "Recursion", "name_cn": "递归", "slug": "recursion"}, {"name_en": "Linked List", "name_cn": "链表", "slug": "linked-list"}], "tags_cn": ["递归", "链表"], "tags_en": ["Recursion", "Linked List"], "url": "https://leetcode.cn/problems/merge-two-sorted-lists/"}, {"index": 28, "id": "2", "frontend_id": "2", "title_en": "Add Two Numbers", "title_cn": "两数相加", "title_slug": "add-two-numbers", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "链表", "paid_only": false, "tags": [{"name_en": "Recursion", "name_cn": "递归", "slug": "recursion"}, {"name_en": "Linked List", "name_cn": "链表", "slug": "linked-list"}, {"name_en": "Math", "name_cn": "数学", "slug": "math"}], "tags_cn": ["递归", "链表", "数学"], "tags_en": ["Recursion", "Linked List", "Math"], "url": "https://leetcode.cn/problems/add-two-numbers/"}, {"index": 29, "id": "19", "frontend_id": "19", "title_en": "Remove Nth Node From End of List", "title_cn": "删除链表的倒数第 N 个结点", "title_slug": "remove-nth-node-from-end-of-list", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "链表", "paid_only": false, "tags": [{"name_en": "Linked List", "name_cn": "链表", "slug": "linked-list"}, {"name_en": "Two Pointers", "name_cn": "双指针", "slug": "two-pointers"}], "tags_cn": ["链表", "双指针"], "tags_en": ["Linked List", "Two Pointers"], "url": "https://leetcode.cn/problems/remove-nth-node-from-end-of-list/"}, {"index": 30, "id": "24", "frontend_id": "24", "title_en": "Swap Nodes in Pairs", "title_cn": "两两交换链表中的节点", "title_slug": "swap-nodes-in-pairs", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "链表", "paid_only": false, "tags": [{"name_en": "Recursion", "name_cn": "递归", "slug": "recursion"}, {"name_en": "Linked List", "name_cn": "链表", "slug": "linked-list"}], "tags_cn": ["递归", "链表"], "tags_en": ["Recursion", "Linked List"], "url": "https://leetcode.cn/problems/swap-nodes-in-pairs/"}, {"index": 31, "id": "25", "frontend_id": "25", "title_en": "Reverse Nodes in k-Group", "title_cn": "K 个一组翻转链表", "title_slug": "reverse-nodes-in-k-group", "difficulty": "困难", "difficulty_en": "HARD", "category": "链表", "paid_only": false, "tags": [{"name_en": "Recursion", "name_cn": "递归", "slug": "recursion"}, {"name_en": "Linked List", "name_cn": "链表", "slug": "linked-list"}], "tags_cn": ["递归", "链表"], "tags_en": ["Recursion", "Linked List"], "url": "https://leetcode.cn/problems/reverse-nodes-in-k-group/"}, {"index": 32, "id": "138", "frontend_id": "138", "title_en": "Copy List with <PERSON>", "title_cn": "随机链表的复制", "title_slug": "copy-list-with-random-pointer", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "链表", "paid_only": false, "tags": [{"name_en": "Hash Table", "name_cn": "哈希表", "slug": "hash-table"}, {"name_en": "Linked List", "name_cn": "链表", "slug": "linked-list"}], "tags_cn": ["哈希表", "链表"], "tags_en": ["Hash Table", "Linked List"], "url": "https://leetcode.cn/problems/copy-list-with-random-pointer/"}, {"index": 33, "id": "148", "frontend_id": "148", "title_en": "Sort List", "title_cn": "排序链表", "title_slug": "sort-list", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "链表", "paid_only": false, "tags": [{"name_en": "Linked List", "name_cn": "链表", "slug": "linked-list"}, {"name_en": "Two Pointers", "name_cn": "双指针", "slug": "two-pointers"}, {"name_en": "Divide and Conquer", "name_cn": "分治", "slug": "divide-and-conquer"}, {"name_en": "Sorting", "name_cn": "排序", "slug": "sorting"}, {"name_en": "<PERSON><PERSON>", "name_cn": "归并排序", "slug": "merge-sort"}], "tags_cn": ["链表", "双指针", "分治", "排序", "归并排序"], "tags_en": ["Linked List", "Two Pointers", "Divide and Conquer", "Sorting", "<PERSON><PERSON>"], "url": "https://leetcode.cn/problems/sort-list/"}, {"index": 34, "id": "23", "frontend_id": "23", "title_en": "<PERSON>rge k <PERSON> Lists", "title_cn": "合并 K 个升序链表", "title_slug": "merge-k-sorted-lists", "difficulty": "困难", "difficulty_en": "HARD", "category": "链表", "paid_only": false, "tags": [{"name_en": "Linked List", "name_cn": "链表", "slug": "linked-list"}, {"name_en": "Divide and Conquer", "name_cn": "分治", "slug": "divide-and-conquer"}, {"name_en": "<PERSON><PERSON> (Priority Queue)", "name_cn": "堆（优先队列）", "slug": "heap-priority-queue"}, {"name_en": "<PERSON><PERSON>", "name_cn": "归并排序", "slug": "merge-sort"}], "tags_cn": ["链表", "分治", "堆（优先队列）", "归并排序"], "tags_en": ["Linked List", "Divide and Conquer", "<PERSON><PERSON> (Priority Queue)", "<PERSON><PERSON>"], "url": "https://leetcode.cn/problems/merge-k-sorted-lists/"}, {"index": 35, "id": "146", "frontend_id": "146", "title_en": "LRU Cache", "title_cn": "LRU 缓存", "title_slug": "lru-cache", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "链表", "paid_only": false, "tags": [{"name_en": "Design", "name_cn": "设计", "slug": "design"}, {"name_en": "Hash Table", "name_cn": "哈希表", "slug": "hash-table"}, {"name_en": "Linked List", "name_cn": "链表", "slug": "linked-list"}, {"name_en": "Doubly-Linked List", "name_cn": "双向链表", "slug": "doubly-linked-list"}], "tags_cn": ["设计", "哈希表", "链表", "双向链表"], "tags_en": ["Design", "Hash Table", "Linked List", "Doubly-Linked List"], "url": "https://leetcode.cn/problems/lru-cache/"}, {"index": 36, "id": "94", "frontend_id": "94", "title_en": "Binary Tree Inorder Traversal", "title_cn": "二叉树的中序遍历", "title_slug": "binary-tree-inorder-traversal", "difficulty": "简单", "difficulty_en": "EASY", "category": "二叉树", "paid_only": false, "tags": [{"name_en": "<PERSON><PERSON>", "name_cn": "栈", "slug": "stack"}, {"name_en": "Tree", "name_cn": "树", "slug": "tree"}, {"name_en": "Depth-First Search", "name_cn": "深度优先搜索", "slug": "depth-first-search"}, {"name_en": "Binary Tree", "name_cn": "二叉树", "slug": "binary-tree"}], "tags_cn": ["栈", "树", "深度优先搜索", "二叉树"], "tags_en": ["<PERSON><PERSON>", "Tree", "Depth-First Search", "Binary Tree"], "url": "https://leetcode.cn/problems/binary-tree-inorder-traversal/"}, {"index": 37, "id": "104", "frontend_id": "104", "title_en": "Maximum Depth of Binary Tree", "title_cn": "二叉树的最大深度", "title_slug": "maximum-depth-of-binary-tree", "difficulty": "简单", "difficulty_en": "EASY", "category": "二叉树", "paid_only": false, "tags": [{"name_en": "Tree", "name_cn": "树", "slug": "tree"}, {"name_en": "Depth-First Search", "name_cn": "深度优先搜索", "slug": "depth-first-search"}, {"name_en": "Breadth-First Search", "name_cn": "广度优先搜索", "slug": "breadth-first-search"}, {"name_en": "Binary Tree", "name_cn": "二叉树", "slug": "binary-tree"}], "tags_cn": ["树", "深度优先搜索", "广度优先搜索", "二叉树"], "tags_en": ["Tree", "Depth-First Search", "Breadth-First Search", "Binary Tree"], "url": "https://leetcode.cn/problems/maximum-depth-of-binary-tree/"}, {"index": 38, "id": "226", "frontend_id": "226", "title_en": "Invert Binary Tree", "title_cn": "翻转二叉树", "title_slug": "invert-binary-tree", "difficulty": "简单", "difficulty_en": "EASY", "category": "二叉树", "paid_only": false, "tags": [{"name_en": "Tree", "name_cn": "树", "slug": "tree"}, {"name_en": "Depth-First Search", "name_cn": "深度优先搜索", "slug": "depth-first-search"}, {"name_en": "Breadth-First Search", "name_cn": "广度优先搜索", "slug": "breadth-first-search"}, {"name_en": "Binary Tree", "name_cn": "二叉树", "slug": "binary-tree"}], "tags_cn": ["树", "深度优先搜索", "广度优先搜索", "二叉树"], "tags_en": ["Tree", "Depth-First Search", "Breadth-First Search", "Binary Tree"], "url": "https://leetcode.cn/problems/invert-binary-tree/"}, {"index": 39, "id": "101", "frontend_id": "101", "title_en": "Symmetric Tree", "title_cn": "对称二叉树", "title_slug": "symmetric-tree", "difficulty": "简单", "difficulty_en": "EASY", "category": "二叉树", "paid_only": false, "tags": [{"name_en": "Tree", "name_cn": "树", "slug": "tree"}, {"name_en": "Depth-First Search", "name_cn": "深度优先搜索", "slug": "depth-first-search"}, {"name_en": "Breadth-First Search", "name_cn": "广度优先搜索", "slug": "breadth-first-search"}, {"name_en": "Binary Tree", "name_cn": "二叉树", "slug": "binary-tree"}], "tags_cn": ["树", "深度优先搜索", "广度优先搜索", "二叉树"], "tags_en": ["Tree", "Depth-First Search", "Breadth-First Search", "Binary Tree"], "url": "https://leetcode.cn/problems/symmetric-tree/"}, {"index": 40, "id": "543", "frontend_id": "543", "title_en": "Diameter of Binary Tree", "title_cn": "二叉树的直径", "title_slug": "diameter-of-binary-tree", "difficulty": "简单", "difficulty_en": "EASY", "category": "二叉树", "paid_only": false, "tags": [{"name_en": "Tree", "name_cn": "树", "slug": "tree"}, {"name_en": "Depth-First Search", "name_cn": "深度优先搜索", "slug": "depth-first-search"}, {"name_en": "Binary Tree", "name_cn": "二叉树", "slug": "binary-tree"}], "tags_cn": ["树", "深度优先搜索", "二叉树"], "tags_en": ["Tree", "Depth-First Search", "Binary Tree"], "url": "https://leetcode.cn/problems/diameter-of-binary-tree/"}, {"index": 41, "id": "102", "frontend_id": "102", "title_en": "Binary Tree Level Order Traversal", "title_cn": "二叉树的层序遍历", "title_slug": "binary-tree-level-order-traversal", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "二叉树", "paid_only": false, "tags": [{"name_en": "Tree", "name_cn": "树", "slug": "tree"}, {"name_en": "Breadth-First Search", "name_cn": "广度优先搜索", "slug": "breadth-first-search"}, {"name_en": "Binary Tree", "name_cn": "二叉树", "slug": "binary-tree"}], "tags_cn": ["树", "广度优先搜索", "二叉树"], "tags_en": ["Tree", "Breadth-First Search", "Binary Tree"], "url": "https://leetcode.cn/problems/binary-tree-level-order-traversal/"}, {"index": 42, "id": "108", "frontend_id": "108", "title_en": "Convert Sorted Array to Binary Search Tree", "title_cn": "将有序数组转换为二叉搜索树", "title_slug": "convert-sorted-array-to-binary-search-tree", "difficulty": "简单", "difficulty_en": "EASY", "category": "二叉树", "paid_only": false, "tags": [{"name_en": "Tree", "name_cn": "树", "slug": "tree"}, {"name_en": "Binary Search Tree", "name_cn": "二叉搜索树", "slug": "binary-search-tree"}, {"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Divide and Conquer", "name_cn": "分治", "slug": "divide-and-conquer"}, {"name_en": "Binary Tree", "name_cn": "二叉树", "slug": "binary-tree"}], "tags_cn": ["树", "二叉搜索树", "数组", "分治", "二叉树"], "tags_en": ["Tree", "Binary Search Tree", "Array", "Divide and Conquer", "Binary Tree"], "url": "https://leetcode.cn/problems/convert-sorted-array-to-binary-search-tree/"}, {"index": 43, "id": "98", "frontend_id": "98", "title_en": "Validate Binary Search Tree", "title_cn": "验证二叉搜索树", "title_slug": "validate-binary-search-tree", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "二叉树", "paid_only": false, "tags": [{"name_en": "Tree", "name_cn": "树", "slug": "tree"}, {"name_en": "Depth-First Search", "name_cn": "深度优先搜索", "slug": "depth-first-search"}, {"name_en": "Binary Search Tree", "name_cn": "二叉搜索树", "slug": "binary-search-tree"}, {"name_en": "Binary Tree", "name_cn": "二叉树", "slug": "binary-tree"}], "tags_cn": ["树", "深度优先搜索", "二叉搜索树", "二叉树"], "tags_en": ["Tree", "Depth-First Search", "Binary Search Tree", "Binary Tree"], "url": "https://leetcode.cn/problems/validate-binary-search-tree/"}, {"index": 44, "id": "230", "frontend_id": "230", "title_en": "Kth Smallest Element in a BST", "title_cn": "二叉搜索树中第 K 小的元素", "title_slug": "kth-smallest-element-in-a-bst", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "二叉树", "paid_only": false, "tags": [{"name_en": "Tree", "name_cn": "树", "slug": "tree"}, {"name_en": "Depth-First Search", "name_cn": "深度优先搜索", "slug": "depth-first-search"}, {"name_en": "Binary Search Tree", "name_cn": "二叉搜索树", "slug": "binary-search-tree"}, {"name_en": "Binary Tree", "name_cn": "二叉树", "slug": "binary-tree"}], "tags_cn": ["树", "深度优先搜索", "二叉搜索树", "二叉树"], "tags_en": ["Tree", "Depth-First Search", "Binary Search Tree", "Binary Tree"], "url": "https://leetcode.cn/problems/kth-smallest-element-in-a-bst/"}, {"index": 45, "id": "199", "frontend_id": "199", "title_en": "Binary Tree Right Side View", "title_cn": "二叉树的右视图", "title_slug": "binary-tree-right-side-view", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "二叉树", "paid_only": false, "tags": [{"name_en": "Tree", "name_cn": "树", "slug": "tree"}, {"name_en": "Depth-First Search", "name_cn": "深度优先搜索", "slug": "depth-first-search"}, {"name_en": "Breadth-First Search", "name_cn": "广度优先搜索", "slug": "breadth-first-search"}, {"name_en": "Binary Tree", "name_cn": "二叉树", "slug": "binary-tree"}], "tags_cn": ["树", "深度优先搜索", "广度优先搜索", "二叉树"], "tags_en": ["Tree", "Depth-First Search", "Breadth-First Search", "Binary Tree"], "url": "https://leetcode.cn/problems/binary-tree-right-side-view/"}, {"index": 46, "id": "114", "frontend_id": "114", "title_en": "Flatten Binary Tree to Linked List", "title_cn": "二叉树展开为链表", "title_slug": "flatten-binary-tree-to-linked-list", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "二叉树", "paid_only": false, "tags": [{"name_en": "<PERSON><PERSON>", "name_cn": "栈", "slug": "stack"}, {"name_en": "Tree", "name_cn": "树", "slug": "tree"}, {"name_en": "Depth-First Search", "name_cn": "深度优先搜索", "slug": "depth-first-search"}, {"name_en": "Linked List", "name_cn": "链表", "slug": "linked-list"}, {"name_en": "Binary Tree", "name_cn": "二叉树", "slug": "binary-tree"}], "tags_cn": ["栈", "树", "深度优先搜索", "链表", "二叉树"], "tags_en": ["<PERSON><PERSON>", "Tree", "Depth-First Search", "Linked List", "Binary Tree"], "url": "https://leetcode.cn/problems/flatten-binary-tree-to-linked-list/"}, {"index": 47, "id": "105", "frontend_id": "105", "title_en": "Construct Binary Tree from Preorder and Inorder Traversal", "title_cn": "从前序与中序遍历序列构造二叉树", "title_slug": "construct-binary-tree-from-preorder-and-inorder-traversal", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "二叉树", "paid_only": false, "tags": [{"name_en": "Tree", "name_cn": "树", "slug": "tree"}, {"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Hash Table", "name_cn": "哈希表", "slug": "hash-table"}, {"name_en": "Divide and Conquer", "name_cn": "分治", "slug": "divide-and-conquer"}, {"name_en": "Binary Tree", "name_cn": "二叉树", "slug": "binary-tree"}], "tags_cn": ["树", "数组", "哈希表", "分治", "二叉树"], "tags_en": ["Tree", "Array", "Hash Table", "Divide and Conquer", "Binary Tree"], "url": "https://leetcode.cn/problems/construct-binary-tree-from-preorder-and-inorder-traversal/"}, {"index": 48, "id": "437", "frontend_id": "437", "title_en": "Path Sum III", "title_cn": "路径总和 III", "title_slug": "path-sum-iii", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "二叉树", "paid_only": false, "tags": [{"name_en": "Tree", "name_cn": "树", "slug": "tree"}, {"name_en": "Depth-First Search", "name_cn": "深度优先搜索", "slug": "depth-first-search"}, {"name_en": "Binary Tree", "name_cn": "二叉树", "slug": "binary-tree"}], "tags_cn": ["树", "深度优先搜索", "二叉树"], "tags_en": ["Tree", "Depth-First Search", "Binary Tree"], "url": "https://leetcode.cn/problems/path-sum-iii/"}, {"index": 49, "id": "236", "frontend_id": "236", "title_en": "Lowest Common Ancestor of a Binary Tree", "title_cn": "二叉树的最近公共祖先", "title_slug": "lowest-common-ancestor-of-a-binary-tree", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "二叉树", "paid_only": false, "tags": [{"name_en": "Tree", "name_cn": "树", "slug": "tree"}, {"name_en": "Depth-First Search", "name_cn": "深度优先搜索", "slug": "depth-first-search"}, {"name_en": "Binary Tree", "name_cn": "二叉树", "slug": "binary-tree"}], "tags_cn": ["树", "深度优先搜索", "二叉树"], "tags_en": ["Tree", "Depth-First Search", "Binary Tree"], "url": "https://leetcode.cn/problems/lowest-common-ancestor-of-a-binary-tree/"}, {"index": 50, "id": "124", "frontend_id": "124", "title_en": "Binary Tree Maximum Path Sum", "title_cn": "二叉树中的最大路径和", "title_slug": "binary-tree-maximum-path-sum", "difficulty": "困难", "difficulty_en": "HARD", "category": "二叉树", "paid_only": false, "tags": [{"name_en": "Tree", "name_cn": "树", "slug": "tree"}, {"name_en": "Depth-First Search", "name_cn": "深度优先搜索", "slug": "depth-first-search"}, {"name_en": "Dynamic Programming", "name_cn": "动态规划", "slug": "dynamic-programming"}, {"name_en": "Binary Tree", "name_cn": "二叉树", "slug": "binary-tree"}], "tags_cn": ["树", "深度优先搜索", "动态规划", "二叉树"], "tags_en": ["Tree", "Depth-First Search", "Dynamic Programming", "Binary Tree"], "url": "https://leetcode.cn/problems/binary-tree-maximum-path-sum/"}, {"index": 51, "id": "200", "frontend_id": "200", "title_en": "Number of Islands", "title_cn": "岛屿数量", "title_slug": "number-of-islands", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "图论", "paid_only": false, "tags": [{"name_en": "Depth-First Search", "name_cn": "深度优先搜索", "slug": "depth-first-search"}, {"name_en": "Breadth-First Search", "name_cn": "广度优先搜索", "slug": "breadth-first-search"}, {"name_en": "Union Find", "name_cn": "并查集", "slug": "union-find"}, {"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Matrix", "name_cn": "矩阵", "slug": "matrix"}], "tags_cn": ["深度优先搜索", "广度优先搜索", "并查集", "数组", "矩阵"], "tags_en": ["Depth-First Search", "Breadth-First Search", "Union Find", "Array", "Matrix"], "url": "https://leetcode.cn/problems/number-of-islands/"}, {"index": 52, "id": "1036", "frontend_id": "994", "title_en": "Rotting Oranges", "title_cn": "腐烂的橘子", "title_slug": "rotting-oranges", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "图论", "paid_only": false, "tags": [{"name_en": "Breadth-First Search", "name_cn": "广度优先搜索", "slug": "breadth-first-search"}, {"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Matrix", "name_cn": "矩阵", "slug": "matrix"}], "tags_cn": ["广度优先搜索", "数组", "矩阵"], "tags_en": ["Breadth-First Search", "Array", "Matrix"], "url": "https://leetcode.cn/problems/rotting-oranges/"}, {"index": 53, "id": "207", "frontend_id": "207", "title_en": "Course Schedule", "title_cn": "课程表", "title_slug": "course-schedule", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "图论", "paid_only": false, "tags": [{"name_en": "Depth-First Search", "name_cn": "深度优先搜索", "slug": "depth-first-search"}, {"name_en": "Breadth-First Search", "name_cn": "广度优先搜索", "slug": "breadth-first-search"}, {"name_en": "Graph", "name_cn": "图", "slug": "graph"}, {"name_en": "Topological Sort", "name_cn": "拓扑排序", "slug": "topological-sort"}], "tags_cn": ["深度优先搜索", "广度优先搜索", "图", "拓扑排序"], "tags_en": ["Depth-First Search", "Breadth-First Search", "Graph", "Topological Sort"], "url": "https://leetcode.cn/problems/course-schedule/"}, {"index": 54, "id": "208", "frontend_id": "208", "title_en": "Implement Trie (Prefix Tree)", "title_cn": "实现 Trie (前缀树)", "title_slug": "implement-trie-prefix-tree", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "图论", "paid_only": false, "tags": [{"name_en": "Design", "name_cn": "设计", "slug": "design"}, {"name_en": "<PERSON><PERSON>", "name_cn": "字典树", "slug": "trie"}, {"name_en": "Hash Table", "name_cn": "哈希表", "slug": "hash-table"}, {"name_en": "String", "name_cn": "字符串", "slug": "string"}], "tags_cn": ["设计", "字典树", "哈希表", "字符串"], "tags_en": ["Design", "<PERSON><PERSON>", "Hash Table", "String"], "url": "https://leetcode.cn/problems/implement-trie-prefix-tree/"}, {"index": 55, "id": "46", "frontend_id": "46", "title_en": "Permutations", "title_cn": "全排列", "title_slug": "permutations", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "回溯", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Backtracking", "name_cn": "回溯", "slug": "backtracking"}], "tags_cn": ["数组", "回溯"], "tags_en": ["Array", "Backtracking"], "url": "https://leetcode.cn/problems/permutations/"}, {"index": 56, "id": "78", "frontend_id": "78", "title_en": "Subsets", "title_cn": "子集", "title_slug": "subsets", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "回溯", "paid_only": false, "tags": [{"name_en": "Bit Manipulation", "name_cn": "位运算", "slug": "bit-manipulation"}, {"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Backtracking", "name_cn": "回溯", "slug": "backtracking"}], "tags_cn": ["位运算", "数组", "回溯"], "tags_en": ["Bit Manipulation", "Array", "Backtracking"], "url": "https://leetcode.cn/problems/subsets/"}, {"index": 57, "id": "17", "frontend_id": "17", "title_en": "Letter Combinations of a Phone Number", "title_cn": "电话号码的字母组合", "title_slug": "letter-combinations-of-a-phone-number", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "回溯", "paid_only": false, "tags": [{"name_en": "Hash Table", "name_cn": "哈希表", "slug": "hash-table"}, {"name_en": "String", "name_cn": "字符串", "slug": "string"}, {"name_en": "Backtracking", "name_cn": "回溯", "slug": "backtracking"}], "tags_cn": ["哈希表", "字符串", "回溯"], "tags_en": ["Hash Table", "String", "Backtracking"], "url": "https://leetcode.cn/problems/letter-combinations-of-a-phone-number/"}, {"index": 58, "id": "39", "frontend_id": "39", "title_en": "Combination Sum", "title_cn": "组合总和", "title_slug": "combination-sum", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "回溯", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Backtracking", "name_cn": "回溯", "slug": "backtracking"}], "tags_cn": ["数组", "回溯"], "tags_en": ["Array", "Backtracking"], "url": "https://leetcode.cn/problems/combination-sum/"}, {"index": 59, "id": "22", "frontend_id": "22", "title_en": "Generate Parentheses", "title_cn": "括号生成", "title_slug": "generate-parentheses", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "回溯", "paid_only": false, "tags": [{"name_en": "String", "name_cn": "字符串", "slug": "string"}, {"name_en": "Dynamic Programming", "name_cn": "动态规划", "slug": "dynamic-programming"}, {"name_en": "Backtracking", "name_cn": "回溯", "slug": "backtracking"}], "tags_cn": ["字符串", "动态规划", "回溯"], "tags_en": ["String", "Dynamic Programming", "Backtracking"], "url": "https://leetcode.cn/problems/generate-parentheses/"}, {"index": 60, "id": "79", "frontend_id": "79", "title_en": "Word Search", "title_cn": "单词搜索", "title_slug": "word-search", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "回溯", "paid_only": false, "tags": [{"name_en": "Depth-First Search", "name_cn": "深度优先搜索", "slug": "depth-first-search"}, {"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "String", "name_cn": "字符串", "slug": "string"}, {"name_en": "Backtracking", "name_cn": "回溯", "slug": "backtracking"}, {"name_en": "Matrix", "name_cn": "矩阵", "slug": "matrix"}], "tags_cn": ["深度优先搜索", "数组", "字符串", "回溯", "矩阵"], "tags_en": ["Depth-First Search", "Array", "String", "Backtracking", "Matrix"], "url": "https://leetcode.cn/problems/word-search/"}, {"index": 61, "id": "131", "frontend_id": "131", "title_en": "Palindrome Partitioning", "title_cn": "分割回文串", "title_slug": "palindrome-partitioning", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "回溯", "paid_only": false, "tags": [{"name_en": "String", "name_cn": "字符串", "slug": "string"}, {"name_en": "Dynamic Programming", "name_cn": "动态规划", "slug": "dynamic-programming"}, {"name_en": "Backtracking", "name_cn": "回溯", "slug": "backtracking"}], "tags_cn": ["字符串", "动态规划", "回溯"], "tags_en": ["String", "Dynamic Programming", "Backtracking"], "url": "https://leetcode.cn/problems/palindrome-partitioning/"}, {"index": 62, "id": "51", "frontend_id": "51", "title_en": "N-Queens", "title_cn": "N 皇后", "title_slug": "n-queens", "difficulty": "困难", "difficulty_en": "HARD", "category": "回溯", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Backtracking", "name_cn": "回溯", "slug": "backtracking"}], "tags_cn": ["数组", "回溯"], "tags_en": ["Array", "Backtracking"], "url": "https://leetcode.cn/problems/n-queens/"}, {"index": 63, "id": "35", "frontend_id": "35", "title_en": "Search Insert Position", "title_cn": "搜索插入位置", "title_slug": "search-insert-position", "difficulty": "简单", "difficulty_en": "EASY", "category": "二分查找", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Binary Search", "name_cn": "二分查找", "slug": "binary-search"}], "tags_cn": ["数组", "二分查找"], "tags_en": ["Array", "Binary Search"], "url": "https://leetcode.cn/problems/search-insert-position/"}, {"index": 64, "id": "74", "frontend_id": "74", "title_en": "Search a 2D Matrix", "title_cn": "搜索二维矩阵", "title_slug": "search-a-2d-matrix", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "二分查找", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Binary Search", "name_cn": "二分查找", "slug": "binary-search"}, {"name_en": "Matrix", "name_cn": "矩阵", "slug": "matrix"}], "tags_cn": ["数组", "二分查找", "矩阵"], "tags_en": ["Array", "Binary Search", "Matrix"], "url": "https://leetcode.cn/problems/search-a-2d-matrix/"}, {"index": 65, "id": "34", "frontend_id": "34", "title_en": "Find First and Last Position of Element in Sorted Array", "title_cn": "在排序数组中查找元素的第一个和最后一个位置", "title_slug": "find-first-and-last-position-of-element-in-sorted-array", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "二分查找", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Binary Search", "name_cn": "二分查找", "slug": "binary-search"}], "tags_cn": ["数组", "二分查找"], "tags_en": ["Array", "Binary Search"], "url": "https://leetcode.cn/problems/find-first-and-last-position-of-element-in-sorted-array/"}, {"index": 66, "id": "33", "frontend_id": "33", "title_en": "Search in Rotated Sorted Array", "title_cn": "搜索旋转排序数组", "title_slug": "search-in-rotated-sorted-array", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "二分查找", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Binary Search", "name_cn": "二分查找", "slug": "binary-search"}], "tags_cn": ["数组", "二分查找"], "tags_en": ["Array", "Binary Search"], "url": "https://leetcode.cn/problems/search-in-rotated-sorted-array/"}, {"index": 67, "id": "153", "frontend_id": "153", "title_en": "Find Minimum in Rotated Sorted Array", "title_cn": "寻找旋转排序数组中的最小值", "title_slug": "find-minimum-in-rotated-sorted-array", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "二分查找", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Binary Search", "name_cn": "二分查找", "slug": "binary-search"}], "tags_cn": ["数组", "二分查找"], "tags_en": ["Array", "Binary Search"], "url": "https://leetcode.cn/problems/find-minimum-in-rotated-sorted-array/"}, {"index": 68, "id": "4", "frontend_id": "4", "title_en": "Median of Two Sorted Arrays", "title_cn": "寻找两个正序数组的中位数", "title_slug": "median-of-two-sorted-arrays", "difficulty": "困难", "difficulty_en": "HARD", "category": "二分查找", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Binary Search", "name_cn": "二分查找", "slug": "binary-search"}, {"name_en": "Divide and Conquer", "name_cn": "分治", "slug": "divide-and-conquer"}], "tags_cn": ["数组", "二分查找", "分治"], "tags_en": ["Array", "Binary Search", "Divide and Conquer"], "url": "https://leetcode.cn/problems/median-of-two-sorted-arrays/"}, {"index": 69, "id": "20", "frontend_id": "20", "title_en": "Valid Parentheses", "title_cn": "有效的括号", "title_slug": "valid-parentheses", "difficulty": "简单", "difficulty_en": "EASY", "category": "栈", "paid_only": false, "tags": [{"name_en": "<PERSON><PERSON>", "name_cn": "栈", "slug": "stack"}, {"name_en": "String", "name_cn": "字符串", "slug": "string"}], "tags_cn": ["栈", "字符串"], "tags_en": ["<PERSON><PERSON>", "String"], "url": "https://leetcode.cn/problems/valid-parentheses/"}, {"index": 70, "id": "155", "frontend_id": "155", "title_en": "<PERSON>", "title_cn": "最小栈", "title_slug": "min-stack", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "栈", "paid_only": false, "tags": [{"name_en": "<PERSON><PERSON>", "name_cn": "栈", "slug": "stack"}, {"name_en": "Design", "name_cn": "设计", "slug": "design"}], "tags_cn": ["栈", "设计"], "tags_en": ["<PERSON><PERSON>", "Design"], "url": "https://leetcode.cn/problems/min-stack/"}, {"index": 71, "id": "394", "frontend_id": "394", "title_en": "Decode String", "title_cn": "字符串解码", "title_slug": "decode-string", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "栈", "paid_only": false, "tags": [{"name_en": "<PERSON><PERSON>", "name_cn": "栈", "slug": "stack"}, {"name_en": "Recursion", "name_cn": "递归", "slug": "recursion"}, {"name_en": "String", "name_cn": "字符串", "slug": "string"}], "tags_cn": ["栈", "递归", "字符串"], "tags_en": ["<PERSON><PERSON>", "Recursion", "String"], "url": "https://leetcode.cn/problems/decode-string/"}, {"index": 72, "id": "739", "frontend_id": "739", "title_en": "Daily Temperatures", "title_cn": "每日温度", "title_slug": "daily-temperatures", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "栈", "paid_only": false, "tags": [{"name_en": "<PERSON><PERSON>", "name_cn": "栈", "slug": "stack"}, {"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Monotonic Stack", "name_cn": "单调栈", "slug": "monotonic-stack"}], "tags_cn": ["栈", "数组", "单调栈"], "tags_en": ["<PERSON><PERSON>", "Array", "Monotonic Stack"], "url": "https://leetcode.cn/problems/daily-temperatures/"}, {"index": 73, "id": "84", "frontend_id": "84", "title_en": "Largest Rectangle in Histogram", "title_cn": "柱状图中最大的矩形", "title_slug": "largest-rectangle-in-histogram", "difficulty": "困难", "difficulty_en": "HARD", "category": "栈", "paid_only": false, "tags": [{"name_en": "<PERSON><PERSON>", "name_cn": "栈", "slug": "stack"}, {"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Monotonic Stack", "name_cn": "单调栈", "slug": "monotonic-stack"}], "tags_cn": ["栈", "数组", "单调栈"], "tags_en": ["<PERSON><PERSON>", "Array", "Monotonic Stack"], "url": "https://leetcode.cn/problems/largest-rectangle-in-histogram/"}, {"index": 74, "id": "215", "frontend_id": "215", "title_en": "Kth Largest Element in an Array", "title_cn": "数组中的第K个最大元素", "title_slug": "kth-largest-element-in-an-array", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "堆", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Divide and Conquer", "name_cn": "分治", "slug": "divide-and-conquer"}, {"name_en": "Quickselect", "name_cn": "快速选择", "slug": "quickselect"}, {"name_en": "Sorting", "name_cn": "排序", "slug": "sorting"}, {"name_en": "<PERSON><PERSON> (Priority Queue)", "name_cn": "堆（优先队列）", "slug": "heap-priority-queue"}], "tags_cn": ["数组", "分治", "快速选择", "排序", "堆（优先队列）"], "tags_en": ["Array", "Divide and Conquer", "Quickselect", "Sorting", "<PERSON><PERSON> (Priority Queue)"], "url": "https://leetcode.cn/problems/kth-largest-element-in-an-array/"}, {"index": 75, "id": "347", "frontend_id": "347", "title_en": "Top K Frequent Elements", "title_cn": "前 K 个高频元素", "title_slug": "top-k-frequent-elements", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "堆", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Hash Table", "name_cn": "哈希表", "slug": "hash-table"}, {"name_en": "Divide and Conquer", "name_cn": "分治", "slug": "divide-and-conquer"}, {"name_en": "Bucket Sort", "name_cn": "桶排序", "slug": "bucket-sort"}, {"name_en": "Counting", "name_cn": "计数", "slug": "counting"}, {"name_en": "Quickselect", "name_cn": "快速选择", "slug": "quickselect"}, {"name_en": "Sorting", "name_cn": "排序", "slug": "sorting"}, {"name_en": "<PERSON><PERSON> (Priority Queue)", "name_cn": "堆（优先队列）", "slug": "heap-priority-queue"}], "tags_cn": ["数组", "哈希表", "分治", "桶排序", "计数", "快速选择", "排序", "堆（优先队列）"], "tags_en": ["Array", "Hash Table", "Divide and Conquer", "Bucket Sort", "Counting", "Quickselect", "Sorting", "<PERSON><PERSON> (Priority Queue)"], "url": "https://leetcode.cn/problems/top-k-frequent-elements/"}, {"index": 76, "id": "295", "frontend_id": "295", "title_en": "Find Median from Data Stream", "title_cn": "数据流的中位数", "title_slug": "find-median-from-data-stream", "difficulty": "困难", "difficulty_en": "HARD", "category": "堆", "paid_only": false, "tags": [{"name_en": "Design", "name_cn": "设计", "slug": "design"}, {"name_en": "Two Pointers", "name_cn": "双指针", "slug": "two-pointers"}, {"name_en": "Data Stream", "name_cn": "数据流", "slug": "data-stream"}, {"name_en": "Sorting", "name_cn": "排序", "slug": "sorting"}, {"name_en": "<PERSON><PERSON> (Priority Queue)", "name_cn": "堆（优先队列）", "slug": "heap-priority-queue"}], "tags_cn": ["设计", "双指针", "数据流", "排序", "堆（优先队列）"], "tags_en": ["Design", "Two Pointers", "Data Stream", "Sorting", "<PERSON><PERSON> (Priority Queue)"], "url": "https://leetcode.cn/problems/find-median-from-data-stream/"}, {"index": 77, "id": "121", "frontend_id": "121", "title_en": "Best Time to Buy and Sell Stock", "title_cn": "买卖股票的最佳时机", "title_slug": "best-time-to-buy-and-sell-stock", "difficulty": "简单", "difficulty_en": "EASY", "category": "贪心算法", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Dynamic Programming", "name_cn": "动态规划", "slug": "dynamic-programming"}], "tags_cn": ["数组", "动态规划"], "tags_en": ["Array", "Dynamic Programming"], "url": "https://leetcode.cn/problems/best-time-to-buy-and-sell-stock/"}, {"index": 78, "id": "55", "frontend_id": "55", "title_en": "Jump Game", "title_cn": "跳跃游戏", "title_slug": "jump-game", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "贪心算法", "paid_only": false, "tags": [{"name_en": "Greedy", "name_cn": "贪心", "slug": "greedy"}, {"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Dynamic Programming", "name_cn": "动态规划", "slug": "dynamic-programming"}], "tags_cn": ["贪心", "数组", "动态规划"], "tags_en": ["Greedy", "Array", "Dynamic Programming"], "url": "https://leetcode.cn/problems/jump-game/"}, {"index": 79, "id": "45", "frontend_id": "45", "title_en": "Jump Game II", "title_cn": "跳跃游戏 II", "title_slug": "jump-game-ii", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "贪心算法", "paid_only": false, "tags": [{"name_en": "Greedy", "name_cn": "贪心", "slug": "greedy"}, {"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Dynamic Programming", "name_cn": "动态规划", "slug": "dynamic-programming"}], "tags_cn": ["贪心", "数组", "动态规划"], "tags_en": ["Greedy", "Array", "Dynamic Programming"], "url": "https://leetcode.cn/problems/jump-game-ii/"}, {"index": 80, "id": "768", "frontend_id": "763", "title_en": "Partition Labels", "title_cn": "划分字母区间", "title_slug": "partition-labels", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "贪心算法", "paid_only": false, "tags": [{"name_en": "Greedy", "name_cn": "贪心", "slug": "greedy"}, {"name_en": "Hash Table", "name_cn": "哈希表", "slug": "hash-table"}, {"name_en": "Two Pointers", "name_cn": "双指针", "slug": "two-pointers"}, {"name_en": "String", "name_cn": "字符串", "slug": "string"}], "tags_cn": ["贪心", "哈希表", "双指针", "字符串"], "tags_en": ["Greedy", "Hash Table", "Two Pointers", "String"], "url": "https://leetcode.cn/problems/partition-labels/"}, {"index": 81, "id": "70", "frontend_id": "70", "title_en": "Climbing Stairs", "title_cn": "爬楼梯", "title_slug": "climbing-stairs", "difficulty": "简单", "difficulty_en": "EASY", "category": "动态规划", "paid_only": false, "tags": [{"name_en": "Memoization", "name_cn": "记忆化搜索", "slug": "memoization"}, {"name_en": "Math", "name_cn": "数学", "slug": "math"}, {"name_en": "Dynamic Programming", "name_cn": "动态规划", "slug": "dynamic-programming"}], "tags_cn": ["记忆化搜索", "数学", "动态规划"], "tags_en": ["Memoization", "Math", "Dynamic Programming"], "url": "https://leetcode.cn/problems/climbing-stairs/"}, {"index": 82, "id": "118", "frontend_id": "118", "title_en": "<PERSON>'s Triangle", "title_cn": "杨辉三角", "title_slug": "pascals-triangle", "difficulty": "简单", "difficulty_en": "EASY", "category": "动态规划", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Dynamic Programming", "name_cn": "动态规划", "slug": "dynamic-programming"}], "tags_cn": ["数组", "动态规划"], "tags_en": ["Array", "Dynamic Programming"], "url": "https://leetcode.cn/problems/pascals-triangle/"}, {"index": 83, "id": "198", "frontend_id": "198", "title_en": "House Robber", "title_cn": "打家劫舍", "title_slug": "house-robber", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "动态规划", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Dynamic Programming", "name_cn": "动态规划", "slug": "dynamic-programming"}], "tags_cn": ["数组", "动态规划"], "tags_en": ["Array", "Dynamic Programming"], "url": "https://leetcode.cn/problems/house-robber/"}, {"index": 84, "id": "279", "frontend_id": "279", "title_en": "Perfect Squares", "title_cn": "完全平方数", "title_slug": "perfect-squares", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "动态规划", "paid_only": false, "tags": [{"name_en": "Breadth-First Search", "name_cn": "广度优先搜索", "slug": "breadth-first-search"}, {"name_en": "Math", "name_cn": "数学", "slug": "math"}, {"name_en": "Dynamic Programming", "name_cn": "动态规划", "slug": "dynamic-programming"}], "tags_cn": ["广度优先搜索", "数学", "动态规划"], "tags_en": ["Breadth-First Search", "Math", "Dynamic Programming"], "url": "https://leetcode.cn/problems/perfect-squares/"}, {"index": 85, "id": "322", "frontend_id": "322", "title_en": "Coin Change", "title_cn": "零钱兑换", "title_slug": "coin-change", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "动态规划", "paid_only": false, "tags": [{"name_en": "Breadth-First Search", "name_cn": "广度优先搜索", "slug": "breadth-first-search"}, {"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Dynamic Programming", "name_cn": "动态规划", "slug": "dynamic-programming"}], "tags_cn": ["广度优先搜索", "数组", "动态规划"], "tags_en": ["Breadth-First Search", "Array", "Dynamic Programming"], "url": "https://leetcode.cn/problems/coin-change/"}, {"index": 86, "id": "139", "frontend_id": "139", "title_en": "Word Break", "title_cn": "单词拆分", "title_slug": "word-break", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "动态规划", "paid_only": false, "tags": [{"name_en": "<PERSON><PERSON>", "name_cn": "字典树", "slug": "trie"}, {"name_en": "Memoization", "name_cn": "记忆化搜索", "slug": "memoization"}, {"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Hash Table", "name_cn": "哈希表", "slug": "hash-table"}, {"name_en": "String", "name_cn": "字符串", "slug": "string"}, {"name_en": "Dynamic Programming", "name_cn": "动态规划", "slug": "dynamic-programming"}], "tags_cn": ["字典树", "记忆化搜索", "数组", "哈希表", "字符串", "动态规划"], "tags_en": ["<PERSON><PERSON>", "Memoization", "Array", "Hash Table", "String", "Dynamic Programming"], "url": "https://leetcode.cn/problems/word-break/"}, {"index": 87, "id": "300", "frontend_id": "300", "title_en": "Longest Increasing Subsequence", "title_cn": "最长递增子序列", "title_slug": "longest-increasing-subsequence", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "动态规划", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Binary Search", "name_cn": "二分查找", "slug": "binary-search"}, {"name_en": "Dynamic Programming", "name_cn": "动态规划", "slug": "dynamic-programming"}], "tags_cn": ["数组", "二分查找", "动态规划"], "tags_en": ["Array", "Binary Search", "Dynamic Programming"], "url": "https://leetcode.cn/problems/longest-increasing-subsequence/"}, {"index": 88, "id": "152", "frontend_id": "152", "title_en": "Maximum Product Subarray", "title_cn": "乘积最大子数组", "title_slug": "maximum-product-subarray", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "动态规划", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Dynamic Programming", "name_cn": "动态规划", "slug": "dynamic-programming"}], "tags_cn": ["数组", "动态规划"], "tags_en": ["Array", "Dynamic Programming"], "url": "https://leetcode.cn/problems/maximum-product-subarray/"}, {"index": 89, "id": "416", "frontend_id": "416", "title_en": "Partition Equal Subset Sum", "title_cn": "分割等和子集", "title_slug": "partition-equal-subset-sum", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "动态规划", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Dynamic Programming", "name_cn": "动态规划", "slug": "dynamic-programming"}], "tags_cn": ["数组", "动态规划"], "tags_en": ["Array", "Dynamic Programming"], "url": "https://leetcode.cn/problems/partition-equal-subset-sum/"}, {"index": 90, "id": "32", "frontend_id": "32", "title_en": "Longest Valid Parentheses", "title_cn": "最长有效括号", "title_slug": "longest-valid-parentheses", "difficulty": "困难", "difficulty_en": "HARD", "category": "动态规划", "paid_only": false, "tags": [{"name_en": "<PERSON><PERSON>", "name_cn": "栈", "slug": "stack"}, {"name_en": "String", "name_cn": "字符串", "slug": "string"}, {"name_en": "Dynamic Programming", "name_cn": "动态规划", "slug": "dynamic-programming"}], "tags_cn": ["栈", "字符串", "动态规划"], "tags_en": ["<PERSON><PERSON>", "String", "Dynamic Programming"], "url": "https://leetcode.cn/problems/longest-valid-parentheses/"}, {"index": 91, "id": "62", "frontend_id": "62", "title_en": "Unique Paths", "title_cn": "不同路径", "title_slug": "unique-paths", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "多维动态规划", "paid_only": false, "tags": [{"name_en": "Math", "name_cn": "数学", "slug": "math"}, {"name_en": "Dynamic Programming", "name_cn": "动态规划", "slug": "dynamic-programming"}, {"name_en": "Combinatorics", "name_cn": "组合数学", "slug": "combinatorics"}], "tags_cn": ["数学", "动态规划", "组合数学"], "tags_en": ["Math", "Dynamic Programming", "Combinatorics"], "url": "https://leetcode.cn/problems/unique-paths/"}, {"index": 92, "id": "64", "frontend_id": "64", "title_en": "Minimum Path Sum", "title_cn": "最小路径和", "title_slug": "minimum-path-sum", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "多维动态规划", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Dynamic Programming", "name_cn": "动态规划", "slug": "dynamic-programming"}, {"name_en": "Matrix", "name_cn": "矩阵", "slug": "matrix"}], "tags_cn": ["数组", "动态规划", "矩阵"], "tags_en": ["Array", "Dynamic Programming", "Matrix"], "url": "https://leetcode.cn/problems/minimum-path-sum/"}, {"index": 93, "id": "5", "frontend_id": "5", "title_en": "Longest Palindromic Substring", "title_cn": "最长回文子串", "title_slug": "longest-palindromic-substring", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "多维动态规划", "paid_only": false, "tags": [{"name_en": "Two Pointers", "name_cn": "双指针", "slug": "two-pointers"}, {"name_en": "String", "name_cn": "字符串", "slug": "string"}, {"name_en": "Dynamic Programming", "name_cn": "动态规划", "slug": "dynamic-programming"}], "tags_cn": ["双指针", "字符串", "动态规划"], "tags_en": ["Two Pointers", "String", "Dynamic Programming"], "url": "https://leetcode.cn/problems/longest-palindromic-substring/"}, {"index": 94, "id": "1250", "frontend_id": "1143", "title_en": "Longest Common Subsequence", "title_cn": "最长公共子序列", "title_slug": "longest-common-subsequence", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "多维动态规划", "paid_only": false, "tags": [{"name_en": "String", "name_cn": "字符串", "slug": "string"}, {"name_en": "Dynamic Programming", "name_cn": "动态规划", "slug": "dynamic-programming"}], "tags_cn": ["字符串", "动态规划"], "tags_en": ["String", "Dynamic Programming"], "url": "https://leetcode.cn/problems/longest-common-subsequence/"}, {"index": 95, "id": "72", "frontend_id": "72", "title_en": "Edit Distance", "title_cn": "编辑距离", "title_slug": "edit-distance", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "多维动态规划", "paid_only": false, "tags": [{"name_en": "String", "name_cn": "字符串", "slug": "string"}, {"name_en": "Dynamic Programming", "name_cn": "动态规划", "slug": "dynamic-programming"}], "tags_cn": ["字符串", "动态规划"], "tags_en": ["String", "Dynamic Programming"], "url": "https://leetcode.cn/problems/edit-distance/"}, {"index": 96, "id": "136", "frontend_id": "136", "title_en": "Single Number", "title_cn": "只出现一次的数字", "title_slug": "single-number", "difficulty": "简单", "difficulty_en": "EASY", "category": "技巧", "paid_only": false, "tags": [{"name_en": "Bit Manipulation", "name_cn": "位运算", "slug": "bit-manipulation"}, {"name_en": "Array", "name_cn": "数组", "slug": "array"}], "tags_cn": ["位运算", "数组"], "tags_en": ["Bit Manipulation", "Array"], "url": "https://leetcode.cn/problems/single-number/"}, {"index": 97, "id": "169", "frontend_id": "169", "title_en": "Majority Element", "title_cn": "多数元素", "title_slug": "majority-element", "difficulty": "简单", "difficulty_en": "EASY", "category": "技巧", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Hash Table", "name_cn": "哈希表", "slug": "hash-table"}, {"name_en": "Divide and Conquer", "name_cn": "分治", "slug": "divide-and-conquer"}, {"name_en": "Counting", "name_cn": "计数", "slug": "counting"}, {"name_en": "Sorting", "name_cn": "排序", "slug": "sorting"}], "tags_cn": ["数组", "哈希表", "分治", "计数", "排序"], "tags_en": ["Array", "Hash Table", "Divide and Conquer", "Counting", "Sorting"], "url": "https://leetcode.cn/problems/majority-element/"}, {"index": 98, "id": "75", "frontend_id": "75", "title_en": "Sort Colors", "title_cn": "颜色分类", "title_slug": "sort-colors", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "技巧", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Two Pointers", "name_cn": "双指针", "slug": "two-pointers"}, {"name_en": "Sorting", "name_cn": "排序", "slug": "sorting"}], "tags_cn": ["数组", "双指针", "排序"], "tags_en": ["Array", "Two Pointers", "Sorting"], "url": "https://leetcode.cn/problems/sort-colors/"}, {"index": 99, "id": "31", "frontend_id": "31", "title_en": "Next Permutation", "title_cn": "下一个排列", "title_slug": "next-permutation", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "技巧", "paid_only": false, "tags": [{"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Two Pointers", "name_cn": "双指针", "slug": "two-pointers"}], "tags_cn": ["数组", "双指针"], "tags_en": ["Array", "Two Pointers"], "url": "https://leetcode.cn/problems/next-permutation/"}, {"index": 100, "id": "287", "frontend_id": "287", "title_en": "Find the Duplicate Number", "title_cn": "寻找重复数", "title_slug": "find-the-duplicate-number", "difficulty": "中等", "difficulty_en": "MEDIUM", "category": "技巧", "paid_only": false, "tags": [{"name_en": "Bit Manipulation", "name_cn": "位运算", "slug": "bit-manipulation"}, {"name_en": "Array", "name_cn": "数组", "slug": "array"}, {"name_en": "Two Pointers", "name_cn": "双指针", "slug": "two-pointers"}, {"name_en": "Binary Search", "name_cn": "二分查找", "slug": "binary-search"}], "tags_cn": ["位运算", "数组", "双指针", "二分查找"], "tags_en": ["Bit Manipulation", "Array", "Two Pointers", "Binary Search"], "url": "https://leetcode.cn/problems/find-the-duplicate-number/"}]