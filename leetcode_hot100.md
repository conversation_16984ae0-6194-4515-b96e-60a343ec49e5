# LeetCode 热题 100

力扣最受刷题发烧友欢迎的 100 道题

总计: 100 道题目

## 目录

- [哈希](#哈希) (3题)
- [双指针](#双指针) (4题)
- [滑动窗口](#滑动窗口) (2题)
- [子串](#子串) (3题)
- [普通数组](#普通数组) (5题)
- [矩阵](#矩阵) (4题)
- [链表](#链表) (14题)
- [二叉树](#二叉树) (15题)
- [图论](#图论) (4题)
- [回溯](#回溯) (8题)
- [二分查找](#二分查找) (6题)
- [栈](#栈) (5题)
- [堆](#堆) (3题)
- [贪心算法](#贪心算法) (4题)
- [动态规划](#动态规划) (10题)
- [多维动态规划](#多维动态规划) (5题)
- [技巧](#技巧) (5题)

## 哈希

| 序号 | 题号 | 题目 | 难度 | 标签 | 链接 |
|------|------|------|------|------|------|
| 1 | 1 | 两数之和 | 简单 | 数组, 哈希表 | [链接](https://leetcode.cn/problems/two-sum/) |
| 2 | 49 | 字母异位词分组 | 中等 | 数组, 哈希表, 字符串... | [链接](https://leetcode.cn/problems/group-anagrams/) |
| 3 | 128 | 最长连续序列 | 中等 | 并查集, 数组, 哈希表 | [链接](https://leetcode.cn/problems/longest-consecutive-sequence/) |

## 双指针

| 序号 | 题号 | 题目 | 难度 | 标签 | 链接 |
|------|------|------|------|------|------|
| 4 | 283 | 移动零 | 简单 | 数组, 双指针 | [链接](https://leetcode.cn/problems/move-zeroes/) |
| 5 | 11 | 盛最多水的容器 | 中等 | 贪心, 数组, 双指针 | [链接](https://leetcode.cn/problems/container-with-most-water/) |
| 6 | 15 | 三数之和 | 中等 | 数组, 双指针, 排序 | [链接](https://leetcode.cn/problems/3sum/) |
| 7 | 42 | 接雨水 | 困难 | 栈, 数组, 双指针... | [链接](https://leetcode.cn/problems/trapping-rain-water/) |

## 滑动窗口

| 序号 | 题号 | 题目 | 难度 | 标签 | 链接 |
|------|------|------|------|------|------|
| 8 | 3 | 无重复字符的最长子串 | 中等 | 哈希表, 字符串, 滑动窗口 | [链接](https://leetcode.cn/problems/longest-substring-without-repeating-characters/) |
| 9 | 438 | 找到字符串中所有字母异位词 | 中等 | 哈希表, 字符串, 滑动窗口 | [链接](https://leetcode.cn/problems/find-all-anagrams-in-a-string/) |

## 子串

| 序号 | 题号 | 题目 | 难度 | 标签 | 链接 |
|------|------|------|------|------|------|
| 10 | 560 | 和为 K 的子数组 | 中等 | 数组, 哈希表, 前缀和 | [链接](https://leetcode.cn/problems/subarray-sum-equals-k/) |
| 11 | 239 | 滑动窗口最大值 | 困难 | 队列, 数组, 滑动窗口... | [链接](https://leetcode.cn/problems/sliding-window-maximum/) |
| 12 | 76 | 最小覆盖子串 | 困难 | 哈希表, 字符串, 滑动窗口 | [链接](https://leetcode.cn/problems/minimum-window-substring/) |

## 普通数组

| 序号 | 题号 | 题目 | 难度 | 标签 | 链接 |
|------|------|------|------|------|------|
| 13 | 53 | 最大子数组和 | 中等 | 数组, 分治, 动态规划 | [链接](https://leetcode.cn/problems/maximum-subarray/) |
| 14 | 56 | 合并区间 | 中等 | 数组, 排序 | [链接](https://leetcode.cn/problems/merge-intervals/) |
| 15 | 189 | 轮转数组 | 中等 | 数组, 数学, 双指针 | [链接](https://leetcode.cn/problems/rotate-array/) |
| 16 | 238 | 除自身以外数组的乘积 | 中等 | 数组, 前缀和 | [链接](https://leetcode.cn/problems/product-of-array-except-self/) |
| 17 | 41 | 缺失的第一个正数 | 困难 | 数组, 哈希表 | [链接](https://leetcode.cn/problems/first-missing-positive/) |

## 矩阵

| 序号 | 题号 | 题目 | 难度 | 标签 | 链接 |
|------|------|------|------|------|------|
| 18 | 73 | 矩阵置零 | 中等 | 数组, 哈希表, 矩阵 | [链接](https://leetcode.cn/problems/set-matrix-zeroes/) |
| 19 | 54 | 螺旋矩阵 | 中等 | 数组, 矩阵, 模拟 | [链接](https://leetcode.cn/problems/spiral-matrix/) |
| 20 | 48 | 旋转图像 | 中等 | 数组, 数学, 矩阵 | [链接](https://leetcode.cn/problems/rotate-image/) |
| 21 | 240 | 搜索二维矩阵 II | 中等 | 数组, 二分查找, 分治... | [链接](https://leetcode.cn/problems/search-a-2d-matrix-ii/) |

## 链表

| 序号 | 题号 | 题目 | 难度 | 标签 | 链接 |
|------|------|------|------|------|------|
| 22 | 160 | 相交链表 | 简单 | 哈希表, 链表, 双指针 | [链接](https://leetcode.cn/problems/intersection-of-two-linked-lists/) |
| 23 | 206 | 反转链表 | 简单 | 递归, 链表 | [链接](https://leetcode.cn/problems/reverse-linked-list/) |
| 24 | 234 | 回文链表 | 简单 | 栈, 递归, 链表... | [链接](https://leetcode.cn/problems/palindrome-linked-list/) |
| 25 | 141 | 环形链表 | 简单 | 哈希表, 链表, 双指针 | [链接](https://leetcode.cn/problems/linked-list-cycle/) |
| 26 | 142 | 环形链表 II | 中等 | 哈希表, 链表, 双指针 | [链接](https://leetcode.cn/problems/linked-list-cycle-ii/) |
| 27 | 21 | 合并两个有序链表 | 简单 | 递归, 链表 | [链接](https://leetcode.cn/problems/merge-two-sorted-lists/) |
| 28 | 2 | 两数相加 | 中等 | 递归, 链表, 数学 | [链接](https://leetcode.cn/problems/add-two-numbers/) |
| 29 | 19 | 删除链表的倒数第 N 个结点 | 中等 | 链表, 双指针 | [链接](https://leetcode.cn/problems/remove-nth-node-from-end-of-list/) |
| 30 | 24 | 两两交换链表中的节点 | 中等 | 递归, 链表 | [链接](https://leetcode.cn/problems/swap-nodes-in-pairs/) |
| 31 | 25 | K 个一组翻转链表 | 困难 | 递归, 链表 | [链接](https://leetcode.cn/problems/reverse-nodes-in-k-group/) |
| 32 | 138 | 随机链表的复制 | 中等 | 哈希表, 链表 | [链接](https://leetcode.cn/problems/copy-list-with-random-pointer/) |
| 33 | 148 | 排序链表 | 中等 | 链表, 双指针, 分治... | [链接](https://leetcode.cn/problems/sort-list/) |
| 34 | 23 | 合并 K 个升序链表 | 困难 | 链表, 分治, 堆（优先队列）... | [链接](https://leetcode.cn/problems/merge-k-sorted-lists/) |
| 35 | 146 | LRU 缓存 | 中等 | 设计, 哈希表, 链表... | [链接](https://leetcode.cn/problems/lru-cache/) |

## 二叉树

| 序号 | 题号 | 题目 | 难度 | 标签 | 链接 |
|------|------|------|------|------|------|
| 36 | 94 | 二叉树的中序遍历 | 简单 | 栈, 树, 深度优先搜索... | [链接](https://leetcode.cn/problems/binary-tree-inorder-traversal/) |
| 37 | 104 | 二叉树的最大深度 | 简单 | 树, 深度优先搜索, 广度优先搜索... | [链接](https://leetcode.cn/problems/maximum-depth-of-binary-tree/) |
| 38 | 226 | 翻转二叉树 | 简单 | 树, 深度优先搜索, 广度优先搜索... | [链接](https://leetcode.cn/problems/invert-binary-tree/) |
| 39 | 101 | 对称二叉树 | 简单 | 树, 深度优先搜索, 广度优先搜索... | [链接](https://leetcode.cn/problems/symmetric-tree/) |
| 40 | 543 | 二叉树的直径 | 简单 | 树, 深度优先搜索, 二叉树 | [链接](https://leetcode.cn/problems/diameter-of-binary-tree/) |
| 41 | 102 | 二叉树的层序遍历 | 中等 | 树, 广度优先搜索, 二叉树 | [链接](https://leetcode.cn/problems/binary-tree-level-order-traversal/) |
| 42 | 108 | 将有序数组转换为二叉搜索树 | 简单 | 树, 二叉搜索树, 数组... | [链接](https://leetcode.cn/problems/convert-sorted-array-to-binary-search-tree/) |
| 43 | 98 | 验证二叉搜索树 | 中等 | 树, 深度优先搜索, 二叉搜索树... | [链接](https://leetcode.cn/problems/validate-binary-search-tree/) |
| 44 | 230 | 二叉搜索树中第 K 小的元素 | 中等 | 树, 深度优先搜索, 二叉搜索树... | [链接](https://leetcode.cn/problems/kth-smallest-element-in-a-bst/) |
| 45 | 199 | 二叉树的右视图 | 中等 | 树, 深度优先搜索, 广度优先搜索... | [链接](https://leetcode.cn/problems/binary-tree-right-side-view/) |
| 46 | 114 | 二叉树展开为链表 | 中等 | 栈, 树, 深度优先搜索... | [链接](https://leetcode.cn/problems/flatten-binary-tree-to-linked-list/) |
| 47 | 105 | 从前序与中序遍历序列构造二叉树 | 中等 | 树, 数组, 哈希表... | [链接](https://leetcode.cn/problems/construct-binary-tree-from-preorder-and-inorder-traversal/) |
| 48 | 437 | 路径总和 III | 中等 | 树, 深度优先搜索, 二叉树 | [链接](https://leetcode.cn/problems/path-sum-iii/) |
| 49 | 236 | 二叉树的最近公共祖先 | 中等 | 树, 深度优先搜索, 二叉树 | [链接](https://leetcode.cn/problems/lowest-common-ancestor-of-a-binary-tree/) |
| 50 | 124 | 二叉树中的最大路径和 | 困难 | 树, 深度优先搜索, 动态规划... | [链接](https://leetcode.cn/problems/binary-tree-maximum-path-sum/) |

## 图论

| 序号 | 题号 | 题目 | 难度 | 标签 | 链接 |
|------|------|------|------|------|------|
| 51 | 200 | 岛屿数量 | 中等 | 深度优先搜索, 广度优先搜索, 并查集... | [链接](https://leetcode.cn/problems/number-of-islands/) |
| 52 | 994 | 腐烂的橘子 | 中等 | 广度优先搜索, 数组, 矩阵 | [链接](https://leetcode.cn/problems/rotting-oranges/) |
| 53 | 207 | 课程表 | 中等 | 深度优先搜索, 广度优先搜索, 图... | [链接](https://leetcode.cn/problems/course-schedule/) |
| 54 | 208 | 实现 Trie (前缀树) | 中等 | 设计, 字典树, 哈希表... | [链接](https://leetcode.cn/problems/implement-trie-prefix-tree/) |

## 回溯

| 序号 | 题号 | 题目 | 难度 | 标签 | 链接 |
|------|------|------|------|------|------|
| 55 | 46 | 全排列 | 中等 | 数组, 回溯 | [链接](https://leetcode.cn/problems/permutations/) |
| 56 | 78 | 子集 | 中等 | 位运算, 数组, 回溯 | [链接](https://leetcode.cn/problems/subsets/) |
| 57 | 17 | 电话号码的字母组合 | 中等 | 哈希表, 字符串, 回溯 | [链接](https://leetcode.cn/problems/letter-combinations-of-a-phone-number/) |
| 58 | 39 | 组合总和 | 中等 | 数组, 回溯 | [链接](https://leetcode.cn/problems/combination-sum/) |
| 59 | 22 | 括号生成 | 中等 | 字符串, 动态规划, 回溯 | [链接](https://leetcode.cn/problems/generate-parentheses/) |
| 60 | 79 | 单词搜索 | 中等 | 深度优先搜索, 数组, 字符串... | [链接](https://leetcode.cn/problems/word-search/) |
| 61 | 131 | 分割回文串 | 中等 | 字符串, 动态规划, 回溯 | [链接](https://leetcode.cn/problems/palindrome-partitioning/) |
| 62 | 51 | N 皇后 | 困难 | 数组, 回溯 | [链接](https://leetcode.cn/problems/n-queens/) |

## 二分查找

| 序号 | 题号 | 题目 | 难度 | 标签 | 链接 |
|------|------|------|------|------|------|
| 63 | 35 | 搜索插入位置 | 简单 | 数组, 二分查找 | [链接](https://leetcode.cn/problems/search-insert-position/) |
| 64 | 74 | 搜索二维矩阵 | 中等 | 数组, 二分查找, 矩阵 | [链接](https://leetcode.cn/problems/search-a-2d-matrix/) |
| 65 | 34 | 在排序数组中查找元素的第一个和最后一个位置 | 中等 | 数组, 二分查找 | [链接](https://leetcode.cn/problems/find-first-and-last-position-of-element-in-sorted-array/) |
| 66 | 33 | 搜索旋转排序数组 | 中等 | 数组, 二分查找 | [链接](https://leetcode.cn/problems/search-in-rotated-sorted-array/) |
| 67 | 153 | 寻找旋转排序数组中的最小值 | 中等 | 数组, 二分查找 | [链接](https://leetcode.cn/problems/find-minimum-in-rotated-sorted-array/) |
| 68 | 4 | 寻找两个正序数组的中位数 | 困难 | 数组, 二分查找, 分治 | [链接](https://leetcode.cn/problems/median-of-two-sorted-arrays/) |

## 栈

| 序号 | 题号 | 题目 | 难度 | 标签 | 链接 |
|------|------|------|------|------|------|
| 69 | 20 | 有效的括号 | 简单 | 栈, 字符串 | [链接](https://leetcode.cn/problems/valid-parentheses/) |
| 70 | 155 | 最小栈 | 中等 | 栈, 设计 | [链接](https://leetcode.cn/problems/min-stack/) |
| 71 | 394 | 字符串解码 | 中等 | 栈, 递归, 字符串 | [链接](https://leetcode.cn/problems/decode-string/) |
| 72 | 739 | 每日温度 | 中等 | 栈, 数组, 单调栈 | [链接](https://leetcode.cn/problems/daily-temperatures/) |
| 73 | 84 | 柱状图中最大的矩形 | 困难 | 栈, 数组, 单调栈 | [链接](https://leetcode.cn/problems/largest-rectangle-in-histogram/) |

## 堆

| 序号 | 题号 | 题目 | 难度 | 标签 | 链接 |
|------|------|------|------|------|------|
| 74 | 215 | 数组中的第K个最大元素 | 中等 | 数组, 分治, 快速选择... | [链接](https://leetcode.cn/problems/kth-largest-element-in-an-array/) |
| 75 | 347 | 前 K 个高频元素 | 中等 | 数组, 哈希表, 分治... | [链接](https://leetcode.cn/problems/top-k-frequent-elements/) |
| 76 | 295 | 数据流的中位数 | 困难 | 设计, 双指针, 数据流... | [链接](https://leetcode.cn/problems/find-median-from-data-stream/) |

## 贪心算法

| 序号 | 题号 | 题目 | 难度 | 标签 | 链接 |
|------|------|------|------|------|------|
| 77 | 121 | 买卖股票的最佳时机 | 简单 | 数组, 动态规划 | [链接](https://leetcode.cn/problems/best-time-to-buy-and-sell-stock/) |
| 78 | 55 | 跳跃游戏 | 中等 | 贪心, 数组, 动态规划 | [链接](https://leetcode.cn/problems/jump-game/) |
| 79 | 45 | 跳跃游戏 II | 中等 | 贪心, 数组, 动态规划 | [链接](https://leetcode.cn/problems/jump-game-ii/) |
| 80 | 763 | 划分字母区间 | 中等 | 贪心, 哈希表, 双指针... | [链接](https://leetcode.cn/problems/partition-labels/) |

## 动态规划

| 序号 | 题号 | 题目 | 难度 | 标签 | 链接 |
|------|------|------|------|------|------|
| 81 | 70 | 爬楼梯 | 简单 | 记忆化搜索, 数学, 动态规划 | [链接](https://leetcode.cn/problems/climbing-stairs/) |
| 82 | 118 | 杨辉三角 | 简单 | 数组, 动态规划 | [链接](https://leetcode.cn/problems/pascals-triangle/) |
| 83 | 198 | 打家劫舍 | 中等 | 数组, 动态规划 | [链接](https://leetcode.cn/problems/house-robber/) |
| 84 | 279 | 完全平方数 | 中等 | 广度优先搜索, 数学, 动态规划 | [链接](https://leetcode.cn/problems/perfect-squares/) |
| 85 | 322 | 零钱兑换 | 中等 | 广度优先搜索, 数组, 动态规划 | [链接](https://leetcode.cn/problems/coin-change/) |
| 86 | 139 | 单词拆分 | 中等 | 字典树, 记忆化搜索, 数组... | [链接](https://leetcode.cn/problems/word-break/) |
| 87 | 300 | 最长递增子序列 | 中等 | 数组, 二分查找, 动态规划 | [链接](https://leetcode.cn/problems/longest-increasing-subsequence/) |
| 88 | 152 | 乘积最大子数组 | 中等 | 数组, 动态规划 | [链接](https://leetcode.cn/problems/maximum-product-subarray/) |
| 89 | 416 | 分割等和子集 | 中等 | 数组, 动态规划 | [链接](https://leetcode.cn/problems/partition-equal-subset-sum/) |
| 90 | 32 | 最长有效括号 | 困难 | 栈, 字符串, 动态规划 | [链接](https://leetcode.cn/problems/longest-valid-parentheses/) |

## 多维动态规划

| 序号 | 题号 | 题目 | 难度 | 标签 | 链接 |
|------|------|------|------|------|------|
| 91 | 62 | 不同路径 | 中等 | 数学, 动态规划, 组合数学 | [链接](https://leetcode.cn/problems/unique-paths/) |
| 92 | 64 | 最小路径和 | 中等 | 数组, 动态规划, 矩阵 | [链接](https://leetcode.cn/problems/minimum-path-sum/) |
| 93 | 5 | 最长回文子串 | 中等 | 双指针, 字符串, 动态规划 | [链接](https://leetcode.cn/problems/longest-palindromic-substring/) |
| 94 | 1143 | 最长公共子序列 | 中等 | 字符串, 动态规划 | [链接](https://leetcode.cn/problems/longest-common-subsequence/) |
| 95 | 72 | 编辑距离 | 中等 | 字符串, 动态规划 | [链接](https://leetcode.cn/problems/edit-distance/) |

## 技巧

| 序号 | 题号 | 题目 | 难度 | 标签 | 链接 |
|------|------|------|------|------|------|
| 96 | 136 | 只出现一次的数字 | 简单 | 位运算, 数组 | [链接](https://leetcode.cn/problems/single-number/) |
| 97 | 169 | 多数元素 | 简单 | 数组, 哈希表, 分治... | [链接](https://leetcode.cn/problems/majority-element/) |
| 98 | 75 | 颜色分类 | 中等 | 数组, 双指针, 排序 | [链接](https://leetcode.cn/problems/sort-colors/) |
| 99 | 31 | 下一个排列 | 中等 | 数组, 双指针 | [链接](https://leetcode.cn/problems/next-permutation/) |
| 100 | 287 | 寻找重复数 | 中等 | 位运算, 数组, 双指针... | [链接](https://leetcode.cn/problems/find-the-duplicate-number/) |

## 统计信息

### 难度分布

- 简单: 20 题
- 中等: 68 题
- 困难: 12 题

### 分类分布

- 哈希: 3 题
- 双指针: 4 题
- 滑动窗口: 2 题
- 子串: 3 题
- 普通数组: 5 题
- 矩阵: 4 题
- 链表: 14 题
- 二叉树: 15 题
- 图论: 4 题
- 回溯: 8 题
- 二分查找: 6 题
- 栈: 5 题
- 堆: 3 题
- 贪心算法: 4 题
- 动态规划: 10 题
- 多维动态规划: 5 题
- 技巧: 5 题
