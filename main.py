#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import argparse
import csv
import json
import re
import sys
import time
from dataclasses import dataclass, asdict
from typing import List, Dict, Set, Tuple

from bs4 import BeautifulSoup, Tag

BASE_URL = "https://leetcode.cn"
TARGET_URL = "https://leetcode.cn/studyplan/top-100-liked/"

USER_AGENT = (
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
    "AppleWebKit/537.36 (KHTML, like Gecko) "
    "Chrome/127.0.0.0 Safari/537.36"
)

@dataclass
class ProblemItem:
    category: str
    title: str
    slug: str
    url: str

def extract_category_for_anchor(a: Tag) -> str:
    """
    从锚点向上/向前寻找最近的分类标题（h1/h2/h3/h4）。
    """
    # 先在父节点链上，寻找包含标题的上级节点
    node: Tag = a
    visited: Set[int] = set()
    while isinstance(node, Tag) and id(node) not in visited:
        visited.add(id(node))
        # 在当前节点内先找标题
        for htag in node.find_all(["h1", "h2", "h3", "h4"], recursive=False):
            text = htag.get_text(strip=True)
            if text:
                return text

        # 再在当前节点的前序兄弟中找最近的标题
        sib = node.previous_sibling
        while sib:
            if isinstance(sib, Tag) and sib.name in ["h1", "h2", "h3", "h4"]:
                text = sib.get_text(strip=True)
                if text:
                    return text
            # 兄弟里如果是个容器，也扫描其末尾可能的标题
            if isinstance(sib, Tag):
                headers = sib.find_all(["h1", "h2", "h3", "h4"])
                if headers:
                    text = headers[-1].get_text(strip=True)
                    if text:
                        return text
            sib = getattr(sib, "previous_sibling", None)

        # 继续向上
        node = node.parent if isinstance(node, Tag) else None

    return ""

def extract_tasks_from_html(html: str) -> List[ProblemItem]:
    """
    从渲染后的 HTML 中提取题目与分类。
    规则：抓取所有 href 形如 /problems/<slug>/ 的链接，尝试就近匹配上方分类标题。
    """
    soup = BeautifulSoup(html, "lxml")
    main = soup.find("main") or soup  # 尽量限制在主内容区

    problem_link_re = re.compile(r"^/problems/([^/]+)/?$")
    items: List[ProblemItem] = []
    seen_slugs: Set[str] = set()

    # 避免导航/页脚等干扰，限制在 main 中查找
    anchors = main.find_all("a", href=problem_link_re)

    for a in anchors:
        href = a.get("href", "").strip()
        m = problem_link_re.match(href)
        if not m:
            continue
        slug = m.group(1)
        if slug in seen_slugs:
            continue

        title = a.get_text(separator=" ", strip=True)
        # 过滤可能的空标题或无效链接
        if not title:
            continue

        category = extract_category_for_anchor(a) or ""

        full_url = BASE_URL + href
        items.append(ProblemItem(
            category=category,
            title=title,
            slug=slug,
            url=full_url
        ))
        seen_slugs.add(slug)

    return items

def fetch_with_playwright(url: str, timeout_ms: int = 30000, headless: bool = True) -> str:
    """
    使用 Playwright 渲染页面并返回完整 HTML。
    """
    from playwright.sync_api import sync_playwright

    with sync_playwright() as p:
        browser = p.chromium.launch(headless=headless)
        context = browser.new_context(user_agent=USER_AGENT, viewport={"width": 1366, "height": 900})
        page = context.new_page()

        page.goto(url, wait_until="networkidle", timeout=timeout_ms)

        # 简单滚动触发懒加载
        page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
        page.wait_for_timeout(800)
        page.evaluate("window.scrollTo(0, 0)")
        page.wait_for_timeout(300)

        content = page.content()
        context.close()
        browser.close()
        return content

def fetch_with_requests(url: str, timeout: int = 20) -> str:
    """
    直接请求页面（仅适用于目标页面 SSR 足够的情况）。
    """
    import requests
    headers = {
        "User-Agent": USER_AGENT,
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
    }
    resp = requests.get(url, headers=headers, timeout=timeout)
    resp.raise_for_status()
    return resp.text

def save_json(items: List[ProblemItem], path: str):
    with open(path, "w", encoding="utf-8") as f:
        json.dump([asdict(i) for i in items], f, ensure_ascii=False, indent=2)

def save_csv(items: List[ProblemItem], path: str):
    with open(path, "w", encoding="utf-8", newline="") as f:
        writer = csv.DictWriter(f, fieldnames=["category", "title", "slug", "url"])
        writer.writeheader()
        for i in items:
            writer.writerow(asdict(i))

def main():
    parser = argparse.ArgumentParser(description="抓取 LeetCode CN Top 100 Liked 题单（含分类名）")
    parser.add_argument("--url", default=TARGET_URL, help="目标题单页 URL（默认：LeetCode CN Top 100 Liked）")
    parser.add_argument("--engine", choices=["playwright", "http"], default="playwright",
                        help="抓取方式：playwright(默认，适合动态页面) 或 http(仅适合 SSR 完整的页面)")
    parser.add_argument("--timeout", type=int, default=30, help="超时时间（秒）")
    parser.add_argument("--headless", action="store_true", help="Playwright 无头模式（默认无头）")
    parser.add_argument("--no-headless", dest="headless", action="store_false", help="Playwright 显示浏览器窗口")
    parser.set_defaults(headless=True)
    parser.add_argument("-o", "--output", default="top_100_liked.json", help="输出文件路径（.json 或 .csv）")
    args = parser.parse_args()

    if args.engine == "playwright":
        html = fetch_with_playwright(args.url, timeout_ms=args.timeout * 1000, headless=args.headless)
    else:
        html = fetch_with_requests(args.url, timeout=args.timeout)

    items = extract_tasks_from_html(html)

    # 根据扩展名决定保存格式
    out = args.output
    if out.lower().endswith(".csv"):
        save_csv(items, out)
    else:
        save_json(items, out)

    print(f"抓取完成，共 {len(items)} 道题，已保存到 {out}")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"抓取失败: {e}", file=sys.stderr)
        sys.exit(1)