#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LeetCode热门100题爬虫
从 https://leetcode.cn/studyplan/top-100-liked/ 页面提取题目信息
"""

import requests
import json
import re
import csv
import pandas as pd
from typing import List, Dict, Any
import time
from urllib.parse import urljoin

class LeetCodeHot100Crawler:
    def __init__(self):
        self.base_url = "https://leetcode.cn"
        self.study_plan_url = "https://leetcode.cn/studyplan/top-100-liked/"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-C<PERSON>,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
    def fetch_page_content(self) -> str:
        """获取页面内容"""
        try:
            print("正在获取页面内容...")
            response = self.session.get(self.study_plan_url, timeout=30)
            response.raise_for_status()
            print(f"页面获取成功，状态码: {response.status_code}")
            return response.text
        except requests.RequestException as e:
            print(f"获取页面失败: {e}")
            raise
    
    def extract_json_data(self, html_content: str) -> Dict[str, Any]:
        """从HTML中提取JSON数据"""
        try:
            print("正在解析页面数据...")
            # 查找包含题目数据的JSON
            json_pattern = r'"props":\s*({.*?})\s*,"page":'
            match = re.search(json_pattern, html_content, re.DOTALL)
            
            if not match:
                raise ValueError("未找到JSON数据")
            
            json_str = match.group(1)
            data = json.loads(json_str)
            print("页面数据解析成功")
            return data
        except (json.JSONDecodeError, ValueError) as e:
            print(f"JSON数据解析失败: {e}")
            raise
    
    def parse_questions(self, json_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """解析题目数据"""
        try:
            print("正在提取题目信息...")
            questions = []
            
            # 导航到题目数据
            study_plan_detail = json_data['pageProps']['dehydratedState']['queries'][0]['state']['data']['studyPlanV2Detail']
            plan_sub_groups = study_plan_detail['planSubGroups']
            
            question_index = 1
            
            for group in plan_sub_groups:
                group_name = group['name']
                group_questions = group['questions']
                
                print(f"处理分组: {group_name} ({len(group_questions)}题)")
                
                for question in group_questions:
                    question_info = {
                        'index': question_index,
                        'id': question['id'],
                        'frontend_id': question['questionFrontendId'],
                        'title_en': question['title'],
                        'title_cn': question['translatedTitle'],
                        'title_slug': question['titleSlug'],
                        'difficulty': self._translate_difficulty(question['difficulty']),
                        'difficulty_en': question['difficulty'],
                        'category': group_name,
                        'paid_only': question['paidOnly'],
                        'tags': [{'name_en': tag['name'], 'name_cn': tag['nameTranslated'], 'slug': tag['slug']} 
                                for tag in question['topicTags']],
                        'tags_cn': [tag['nameTranslated'] for tag in question['topicTags']],
                        'tags_en': [tag['name'] for tag in question['topicTags']],
                        'url': f"https://leetcode.cn/problems/{question['titleSlug']}/"
                    }
                    questions.append(question_info)
                    question_index += 1
            
            print(f"成功提取 {len(questions)} 道题目")
            return questions
            
        except KeyError as e:
            print(f"数据结构解析错误: {e}")
            raise
    
    def _translate_difficulty(self, difficulty: str) -> str:
        """翻译难度等级"""
        difficulty_map = {
            'EASY': '简单',
            'MEDIUM': '中等', 
            'HARD': '困难'
        }
        return difficulty_map.get(difficulty, difficulty)
    
    def save_to_json(self, questions: List[Dict[str, Any]], filename: str = "leetcode_hot100.json"):
        """保存为JSON格式"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(questions, f, ensure_ascii=False, indent=2)
            print(f"数据已保存到 {filename}")
        except Exception as e:
            print(f"保存JSON文件失败: {e}")
    
    def save_to_csv(self, questions: List[Dict[str, Any]], filename: str = "leetcode_hot100.csv"):
        """保存为CSV格式"""
        try:
            # 准备CSV数据
            csv_data = []
            for q in questions:
                csv_row = {
                    '序号': q['index'],
                    '题号': q['frontend_id'],
                    '题目(中文)': q['title_cn'],
                    '题目(英文)': q['title_en'],
                    '难度': q['difficulty'],
                    '分类': q['category'],
                    '标签': ', '.join(q['tags_cn']),
                    '是否付费': '是' if q['paid_only'] else '否',
                    '链接': q['url']
                }
                csv_data.append(csv_row)
            
            df = pd.DataFrame(csv_data)
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"数据已保存到 {filename}")
        except Exception as e:
            print(f"保存CSV文件失败: {e}")
    
    def save_to_markdown(self, questions: List[Dict[str, Any]], filename: str = "leetcode_hot100.md"):
        """保存为Markdown格式"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("# LeetCode 热题 100\n\n")
                f.write("力扣最受刷题发烧友欢迎的 100 道题\n\n")
                f.write(f"总计: {len(questions)} 道题目\n\n")
                
                # 按分类分组
                categories = {}
                for q in questions:
                    category = q['category']
                    if category not in categories:
                        categories[category] = []
                    categories[category].append(q)
                
                # 生成目录
                f.write("## 目录\n\n")
                for category, questions_in_category in categories.items():
                    f.write(f"- [{category}](#{category.replace(' ', '-').lower()}) ({len(questions_in_category)}题)\n")
                f.write("\n")
                
                # 生成详细列表
                for category, questions_in_category in categories.items():
                    f.write(f"## {category}\n\n")
                    f.write("| 序号 | 题号 | 题目 | 难度 | 标签 | 链接 |\n")
                    f.write("|------|------|------|------|------|------|\n")
                    
                    for q in questions_in_category:
                        tags_str = ', '.join(q['tags_cn'][:3])  # 只显示前3个标签
                        if len(q['tags_cn']) > 3:
                            tags_str += "..."
                        
                        f.write(f"| {q['index']} | {q['frontend_id']} | {q['title_cn']} | {q['difficulty']} | {tags_str} | [链接]({q['url']}) |\n")
                    f.write("\n")
                
                # 统计信息
                f.write("## 统计信息\n\n")
                difficulty_count = {}
                for q in questions:
                    diff = q['difficulty']
                    difficulty_count[diff] = difficulty_count.get(diff, 0) + 1
                
                f.write("### 难度分布\n\n")
                for diff, count in difficulty_count.items():
                    f.write(f"- {diff}: {count} 题\n")
                f.write("\n")
                
                f.write("### 分类分布\n\n")
                for category, questions_in_category in categories.items():
                    f.write(f"- {category}: {len(questions_in_category)} 题\n")
            
            print(f"数据已保存到 {filename}")
        except Exception as e:
            print(f"保存Markdown文件失败: {e}")
    
    def crawl_from_cached_data(self):
        """使用缓存的数据进行解析"""
        try:
            print("开始解析LeetCode热门100题数据...")

            # 使用预先获取的数据
            cached_data = self.get_cached_data()

            # 提取题目信息
            questions = self.parse_questions(cached_data)

            # 保存数据
            self.save_to_json(questions)
            self.save_to_csv(questions)
            self.save_to_markdown(questions)

            print("\n=== 解析完成 ===")
            print(f"总共提取了 {len(questions)} 道题目")
            print("文件已生成:")
            print("- leetcode_hot100.json (JSON格式)")
            print("- leetcode_hot100.csv (CSV格式)")
            print("- leetcode_hot100.md (Markdown格式)")

            return questions

        except Exception as e:
            print(f"解析失败: {e}")
            raise

    def get_cached_data(self):
        """返回缓存的页面数据"""
        return {
            "pageProps": {
                "dehydratedState": {
                    "queries": [{
                        "state": {
                            "data": {
                                "studyPlanV2Detail": {
                                    "slug": "top-100-liked",
                                    "name": "LeetCode 热题 100",
                                    "highlight": "力扣最受刷题发烧友欢迎的 100 道题",
                                    "description": "站内用户最喜爱的 100 道题\r\n站内最经典的 100 道题\r\n刷题发烧友的必备题单",
                                    "planSubGroups": [
                                        {
                                            "slug": "top-100-liked-80-r32o",
                                            "name": "哈希",
                                            "premiumOnly": False,
                                            "questionNum": 3,
                                            "questions": [
                                                {
                                                    "translatedTitle": "两数之和",
                                                    "titleSlug": "two-sum",
                                                    "title": "Two Sum",
                                                    "questionFrontendId": "1",
                                                    "paidOnly": False,
                                                    "id": "1",
                                                    "difficulty": "EASY",
                                                    "topicTags": [
                                                        {"slug": "array", "nameTranslated": "数组", "name": "Array"},
                                                        {"slug": "hash-table", "nameTranslated": "哈希表", "name": "Hash Table"}
                                                    ]
                                                },
                                                {
                                                    "translatedTitle": "字母异位词分组",
                                                    "titleSlug": "group-anagrams",
                                                    "title": "Group Anagrams",
                                                    "questionFrontendId": "49",
                                                    "paidOnly": False,
                                                    "id": "49",
                                                    "difficulty": "MEDIUM",
                                                    "topicTags": [
                                                        {"slug": "array", "nameTranslated": "数组", "name": "Array"},
                                                        {"slug": "hash-table", "nameTranslated": "哈希表", "name": "Hash Table"},
                                                        {"slug": "string", "nameTranslated": "字符串", "name": "String"},
                                                        {"slug": "sorting", "nameTranslated": "排序", "name": "Sorting"}
                                                    ]
                                                },
                                                {
                                                    "translatedTitle": "最长连续序列",
                                                    "titleSlug": "longest-consecutive-sequence",
                                                    "title": "Longest Consecutive Sequence",
                                                    "questionFrontendId": "128",
                                                    "paidOnly": False,
                                                    "id": "128",
                                                    "difficulty": "MEDIUM",
                                                    "topicTags": [
                                                        {"slug": "union-find", "nameTranslated": "并查集", "name": "Union Find"},
                                                        {"slug": "array", "nameTranslated": "数组", "name": "Array"},
                                                        {"slug": "hash-table", "nameTranslated": "哈希表", "name": "Hash Table"}
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                }
                            }
                        }
                    }]
                }
            }
        }

    def crawl(self):
        """执行爬取任务"""
        try:
            print("开始爬取LeetCode热门100题...")

            # 尝试在线获取，如果失败则使用缓存数据
            try:
                html_content = self.fetch_page_content()
                json_data = self.extract_json_data(html_content)
            except Exception as e:
                print(f"在线获取失败: {e}")
                print("使用缓存数据进行解析...")
                return self.crawl_from_cached_data()

            # 提取题目信息
            questions = self.parse_questions(json_data)

            # 保存数据
            self.save_to_json(questions)
            self.save_to_csv(questions)
            self.save_to_markdown(questions)

            print("\n=== 爬取完成 ===")
            print(f"总共提取了 {len(questions)} 道题目")
            print("文件已生成:")
            print("- leetcode_hot100.json (JSON格式)")
            print("- leetcode_hot100.csv (CSV格式)")
            print("- leetcode_hot100.md (Markdown格式)")

            return questions

        except Exception as e:
            print(f"爬取失败: {e}")
            raise

def main():
    """主函数"""
    crawler = LeetCodeHot100Crawler()
    questions = crawler.crawl()
    
    # 显示部分结果
    print("\n=== 前5道题目预览 ===")
    for i, q in enumerate(questions[:5]):
        print(f"{i+1}. [{q['frontend_id']}] {q['title_cn']} ({q['difficulty']}) - {q['category']}")

if __name__ == "__main__":
    main()
